/*!***************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/postcss-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./frontend/src/styles/crelancer.scss ***!
  \***************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(63, 131, 248, 0.5);
  --tw-ring-offset-shadow: 0 0 rgba(0,0,0,0);
  --tw-ring-shadow: 0 0 rgba(0,0,0,0);
  --tw-shadow: 0 0 rgba(0,0,0,0);
  --tw-shadow-colored: 0 0 rgba(0,0,0,0);
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(63, 131, 248, 0.5);
  --tw-ring-offset-shadow: 0 0 rgba(0,0,0,0);
  --tw-ring-shadow: 0 0 rgba(0,0,0,0);
  --tw-shadow: 0 0 rgba(0,0,0,0);
  --tw-shadow-colored: 0 0 rgba(0,0,0,0);
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  -webkit-box-sizing: border-box;
          box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #EAECF0; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Montserrat, Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, system-ui, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji; /* 4 */
  -webkit-font-feature-settings: normal;
          font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted currentColor;
          text-decoration: underline dotted currentColor;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  -webkit-font-feature-settings: normal;
          font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  -webkit-font-feature-settings: inherit;
          font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
  opacity: 1; /* 1 */
  color: #98A2B3; /* 2 */
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #98A2B3; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #98A2B3; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #667085;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 rgba(0,0,0,0);
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #1C64F2;
}

input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
  color: #667085;
  opacity: 1;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #667085;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #667085;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: -webkit-inline-box;
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%23667085%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: none;
  background-image: initial;
  background-position: 0 0;
  background-position: initial;
  background-repeat: repeat;
  background-repeat: initial;
  background-size: auto auto;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: inherit;
          print-color-adjust: inherit;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #1C64F2;
  background-color: #fff;
  border-color: #667085;
  border-width: 1px;
  --tw-shadow: 0 0 rgba(0,0,0,0);
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {

  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: transparent none repeat 0 0 / auto auto padding-box border-box scroll;
  background: initial;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: inherit;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

.tooltip-arrow,.tooltip-arrow:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

.tooltip-arrow {
  visibility: hidden;
}

.tooltip-arrow:before {
  content: "";
  visibility: visible;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}

[data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before {
  border-style: solid;
  border-color: #e5e7eb;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

.tooltip[data-popper-placement^='top'] > .tooltip-arrow {
  bottom: -4px;
}

.tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
  top: -4px;
}

.tooltip[data-popper-placement^='left'] > .tooltip-arrow {
  right: -4px;
}

.tooltip[data-popper-placement^='right'] > .tooltip-arrow {
  left: -4px;
}

.tooltip.invisible > .tooltip-arrow:before {
  visibility: hidden;
}

[data-popper-arrow],[data-popper-arrow]:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

[data-popper-arrow] {
  visibility: hidden;
}

[data-popper-arrow]:before {
  content: "";
  visibility: visible;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}

[data-popper-arrow]:after {
  content: "";
  visibility: visible;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  position: absolute;
  width: 9px;
  height: 9px;
  background: inherit;
}

[role="tooltip"] > [data-popper-arrow]:before {
  border-style: solid;
  border-color: #e5e7eb;
}

[role="tooltip"] > [data-popper-arrow]:after {
  border-style: solid;
  border-color: #e5e7eb;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow] {
  bottom: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow] {
  top: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow] {
  right: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow] {
  left: -5px;
}

[role="tooltip"].invisible > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].invisible > [data-popper-arrow]:after {
  visibility: hidden;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #667085;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 rgba(0,0,0,0);
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #1C64F2;
}

input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
  color: #667085;
  opacity: 1;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #667085;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #667085;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  background: none;
}

select:not([size]) {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 10 6%27%3e %3cpath stroke=%27%23667085%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m1 1 4 4 4-4%27/%3e %3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 0.75em 0.75em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[dir=rtl] select:not([size]) {
  background-position: left 0.75rem center;
  padding-right: 0.75rem;
  padding-left: 0;
}

[multiple] {
  background-image: none;
  background-image: initial;
  background-position: 0 0;
  background-position: initial;
  background-repeat: repeat;
  background-repeat: initial;
  background-size: auto auto;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: inherit;
          print-color-adjust: inherit;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #1C64F2;
  background-color: #fff;
  border-color: #667085;
  border-width: 1px;
  --tw-shadow: 0 0 rgba(0,0,0,0);
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 0.55em 0.55em;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 12%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%273%27 d=%27M1 5.917 5.724 10.5 15 1.5%27/%3e %3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
  background-size: 1em 1em;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 12%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%273%27 d=%27M0.5 6h14%27/%3e %3c/svg%3e");
  background-color: currentColor;
  border-color: transparent;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: transparent none repeat 0 0 / auto auto padding-box border-box scroll;
  background: initial;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: inherit;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px auto inherit;
}

[dir="ltr"] input[type=file]::file-selector-button {
  margin-left: -1rem;
}

[dir="rtl"] input[type=file]::file-selector-button {
  margin-right: -1rem;
}

[dir="ltr"] input[type=file]::file-selector-button {
  margin-right: 1rem;
}

[dir="rtl"] input[type=file]::file-selector-button {
  margin-left: 1rem;
}

input[type=file]::file-selector-button {
  color: white;
  background: #1D2939;
  border: 0;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 2rem;
  padding-right: 1rem;
}

input[type=file]::file-selector-button:hover {
  background: #344054;
}

[dir=rtl] input[type=file]::file-selector-button {
  padding-right: 2rem;
  padding-left: 1rem;
}

input[type="range"]::-webkit-slider-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-webkit-slider-thumb {
  background: #98A2B3;
}

input[type="range"]:focus::-webkit-slider-thumb {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
  --tw-ring-opacity: 1px;
  --tw-ring-color: rgba(164, 202, 254, var(--tw-ring-opacity));
}

input[type="range"]::-moz-range-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-moz-range-thumb {
  background: #98A2B3;
}

input[type="range"]::-moz-range-progress {
  background: #3F83F8;
}

input[type="range"]::-ms-fill-lower {
  background: #3F83F8;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.bg-purple-ios {
    background-color: #590696 !important;
    opacity: 1 !important;
  }
body {
  --tw-text-opacity: 1;
  color: rgba(16, 24, 40, 1);
  color: rgba(16, 24, 40, var(--tw-text-opacity, 1));
}
.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1290px;
  padding-left: 1rem;
  padding-right: 1rem;
}
@media (min-width: 768px) {

  .container {
    padding-left: 0px;
    padding-right: 0px;
  }
}
.heading-1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
}
@media (min-width: 768px) {

  .heading-1 {
    font-size: 36px;
    line-height: 40px;
  }
}
label {
    font-weight: 500 !important;
    color: #101828 !important;
  }
input[type=file]::file-selector-button {
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  --tw-bg-opacity: 1;
  background-color: rgba(98, 198, 197, 1);
  background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
}
input[type=file]::file-selector-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(89, 6, 150, 1);
  background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
}
input[type=file]::file-selector-button:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
}
.btn-primary::before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
    -webkit-transform-origin: 0 50%;
            transform-origin: 0 50%;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
    -webkit-transition-duration: 0.3s;
            transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
            transition-timing-function: ease-out;
  }
.btn-primary.white::before {
    background: #fff;
  }
.btn-primary.purple::before {
  --tw-bg-opacity: 1;
  background-color: rgba(89, 6, 150, 1);
  background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
}
.btn-primary.downy::before {
  --tw-bg-opacity: 1;
  background-color: rgba(98, 198, 197, 1);
  background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
}
.btn-primary {
    overflow: hidden;
    -webkit-transform: perspective(1px) translateZ(0);
            transform: perspective(1px) translateZ(0);
    -webkit-transition-property: color;
    transition-property: color;
    -webkit-transition-duration: 0.3s;
            transition-duration: 0.3s;
  }
.btn-primary:hover::before {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
.btn-cookie {
  cursor: pointer;
  text-align: center;
  text-decoration-line: underline;
  text-underline-offset: 2px;
}
@media (min-width: 640px) {

  .btn-cookie {
    text-decoration-line: none;
  }
}
.btn-cookie {
  border-radius: 100px;
  padding-left: 0.375rem;
  padding-right: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
}
@media (min-width: 640px) {

  .btn-cookie {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
.advantage-card {
    position: relative;
  }
.advantage-card .front,
.advantage-card .back {
    position: absolute;
    -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
    width: 100%;
    height: 100%;
  }
@-webkit-keyframes slide-in-top {
    0% {
      -webkit-transform: translateY(-1000px);
              transform: translateY(-1000px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
              transform: translateY(0);
      opacity: 1;
    }
  }
@keyframes slide-in-top {
    0% {
      -webkit-transform: translateY(-1000px);
              transform: translateY(-1000px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
              transform: translateY(0);
      opacity: 1;
    }
  }
.slide-in-top {
    -webkit-animation: slide-in-top 2s ease-in-out both;
            animation: slide-in-top 2s ease-in-out both;
  }
.swiper-button-next.swiper-button-disabled,
.swiper-button-prev.swiper-button-disabled {
    opacity: 1 !important;
  }
.swiper-button-next,
.swiper-button-prev {
    width: 40px !important;
  }
.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 14px !important;
    line-height: 40px !important;
  }
.custom-shadow {
    -webkit-filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.4));
            filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.4));
  }
.get-started-btn:hover .circle {
  --tw-border-opacity: 1;
  border-color: rgba(248, 221, 23, 1);
  border-color: rgba(248, 221, 23, var(--tw-border-opacity, 1));
}
.get-started-btn:hover .icon {
  --tw-text-opacity: 1;
  color: rgba(239, 19, 152, 1);
  color: rgba(239, 19, 152, var(--tw-text-opacity, 1));
}
.sidebar ul li.active a {
  position: relative;
  --tw-text-opacity: 1;
  color: rgba(89, 6, 150, 1);
  color: rgba(89, 6, 150, var(--tw-text-opacity, 1));
}
.sidebar ul li.active a::before {
    content: "";
    position: absolute;
    top: 0;
    left: -4px;
    width: 11px;
    height: 100%;
    background-color: #590696;
    border-radius: 4px;
    border: 4px solid #fff;
  }
@media screen and (max-width: 1024px) {
    .sidebar ul li.active a::before {
      content: none;
    }
  }
label[for^=id_categories_],
label[for^=id_reasons_] {
    display: -webkit-inline-box;
    display: inline-flex;
    -webkit-box-align: center;
            align-items: center;
    gap: 8px;
    line-height: 24px;
  }
label + label {
    margin-top: 1em;
  }
input[type=checkbox] {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
    background-color: #fff;
    margin: 0;
    font: inherit;
    color: #fff;
    width: 16px;
    height: 16px;
    border: 1px solid #d0d5dd;
    border-radius: 4px;
    display: grid;
    align-content: center;
    -webkit-box-pack: center;
            justify-content: center;
    place-content: center;
  }
input[type=checkbox]:checked {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
    background-color: #62c6c5;
    margin: 0;
    font: inherit;
    color: #62c6c5;
    width: 16px;
    height: 16px;
    border: 1px solid #62c6c5;
    border-radius: 4px;
    display: grid;
    align-content: center;
    -webkit-box-pack: center;
            justify-content: center;
    place-content: center;
  }
input[type=checkbox]::before {
    content: "";
    width: 10px;
    height: 10px;
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
    background-color: #fff;
  }
input[type=checkbox]:focus {
    outline: 2px solid #62c6c5;
    outline-offset: 2px;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
.checkbox-delete div {
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-align: center;
          align-items: center;
}
.checkbox-delete div label {
  -webkit-box-ordinal-group: 3;
          order: 2;
  margin-bottom: 0px;
}
.checkbox-delete div input {
  -webkit-box-ordinal-group: 2;
          order: 1;
  margin-right: 0.5rem;
}
.checkbox-delete div input[type=checkbox]:checked {
  margin-right: 0.5rem;
  --tw-border-opacity: 1;
  border-color: rgba(224, 36, 36, 1);
  border-color: rgba(224, 36, 36, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(224, 36, 36, 1);
  background-color: rgba(224, 36, 36, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(224, 36, 36, 1);
  color: rgba(224, 36, 36, var(--tw-text-opacity, 1));
}
.checkbox-delete div input[type=checkbox]:focus {
    outline: 2px solid #e02424;
  }
.category-wrap > div > div > div {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  grid-gap: 0.5rem;
  gap: 0.5rem;
}
@media (min-width: 768px) {

  .category-wrap > div > div > div {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
.reasons-wrap > div > div > div > div {
  margin-bottom: 0.75rem;
}
.crispy-form .alert > div {
  border-width: 0px;
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgba(224, 36, 36, 1);
  color: rgba(224, 36, 36, var(--tw-text-opacity, 1));
}
.list-alphabetic li {
  display: -webkit-box;
  display: flex;
  gap: 0.5rem;
}
.list-alphabetic li div:first-child {
  flex-shrink: 0;
}
.crispy-form .alert > div > ul > li {
  position: relative;
  padding-left: 1.5rem;
}
.crispy-form .alert > div > ul > li::before {
    content: url(data:image/svg+xml;base64,PHN2Zw0KICB3aWR0aD0iMTYiDQogIGhlaWdodD0iMTYiDQogIHZpZXdCb3g9IjAgMCAxNiAxNiINCiAgZmlsbD0ibm9uZSINCiAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIg0KICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIg0KPg0KICA8cGF0aA0KICAgIGZpbGwtcnVsZT0iZXZlbm9kZCINCiAgICBjbGlwLXJ1bGU9ImV2ZW5vZGQiDQogICAgZD0iTTE1LjE5OTggNy45OTk4QzE1LjE5OTggOS45MDkzNiAxNC40NDEyIDExLjc0MDcgMTMuMDkxIDEzLjA5MUMxMS43NDA3IDE0LjQ0MTIgOS45MDkzNiAxNS4xOTk4IDcuOTk5OCAxNS4xOTk4QzYuMDkwMjUgMTUuMTk5OCA0LjI1ODkgMTQuNDQxMiAyLjkwODY0IDEzLjA5MUMxLjU1ODM3IDExLjc0MDcgMC43OTk4MDUgOS45MDkzNiAwLjc5OTgwNSA3Ljk5OThDMC43OTk4MDUgNi4wOTAyNSAxLjU1ODM3IDQuMjU4OSAyLjkwODY0IDIuOTA4NjRDNC4yNTg5IDEuNTU4MzcgNi4wOTAyNSAwLjc5OTgwNSA3Ljk5OTggMC43OTk4MDVDOS45MDkzNiAwLjc5OTgwNSAxMS43NDA3IDEuNTU4MzcgMTMuMDkxIDIuOTA4NjRDMTQuNDQxMiA0LjI1ODkgMTUuMTk5OCA2LjA5MDI1IDE1LjE5OTggNy45OTk4VjcuOTk5OFpNOC44OTk4MSA0LjM5OThDOC44OTk4MSA0LjYzODUgOC44MDQ5OCA0Ljg2NzQyIDguNjM2MiA1LjAzNjJDOC40Njc0MiA1LjIwNDk4IDguMjM4NSA1LjI5OTggNy45OTk4IDUuMjk5OEM3Ljc2MTExIDUuMjk5OCA3LjUzMjE5IDUuMjA0OTggNy4zNjM0MSA1LjAzNjJDNy4xOTQ2MyA0Ljg2NzQyIDcuMDk5OCA0LjYzODUgNy4wOTk4IDQuMzk5OEM3LjA5OTggNC4xNjExMSA3LjE5NDYzIDMuOTMyMTkgNy4zNjM0MSAzLjc2MzQxQzcuNTMyMTkgMy41OTQ2MyA3Ljc2MTExIDMuNDk5OCA3Ljk5OTggMy40OTk4QzguMjM4NSAzLjQ5OTggOC40Njc0MiAzLjU5NDYzIDguNjM2MiAzLjc2MzQxQzguODA0OTggMy45MzIxOSA4Ljg5OTgxIDQuMTYxMTEgOC44OTk4MSA0LjM5OThaTTcuMDk5OCA3LjA5OThDNi44NjExMSA3LjA5OTggNi42MzIxOSA3LjE5NDYzIDYuNDYzNDEgNy4zNjM0MUM2LjI5NDYzIDcuNTMyMTkgNi4xOTk4IDcuNzYxMTEgNi4xOTk4IDcuOTk5OEM2LjE5OTggOC4yMzg1IDYuMjk0NjMgOC40Njc0MiA2LjQ2MzQxIDguNjM2MkM2LjYzMjE5IDguODA0OTggNi44NjExMSA4Ljg5OTgxIDcuMDk5OCA4Ljg5OTgxVjExLjU5OThDNy4wOTk4IDExLjgzODUgNy4xOTQ2MyAxMi4wNjc0IDcuMzYzNDEgMTIuMjM2MkM3LjUzMjE5IDEyLjQwNSA3Ljc2MTExIDEyLjQ5OTggNy45OTk4IDEyLjQ5OThIOC44OTk4MUM5LjEzODUgMTIuNDk5OCA5LjM2NzQyIDEyLjQwNSA5LjUzNjIgMTIuMjM2MkM5LjcwNDk4IDEyLjA2NzQgOS43OTk4MSAxMS44Mzg1IDkuNzk5ODEgMTEuNTk5OEM5Ljc5OTgxIDExLjM2MTEgOS43MDQ5OCAxMS4xMzIyIDkuNTM2MiAxMC45NjM0QzkuMzY3NDIgMTAuNzk0NiA5LjEzODUgMTAuNjk5OCA4Ljg5OTgxIDEwLjY5OThWNy45OTk4QzguODk5ODEgNy43NjExMSA4LjgwNDk4IDcuNTMyMTkgOC42MzYyIDcuMzYzNDFDOC40Njc0MiA3LjE5NDYzIDguMjM4NSA3LjA5OTggNy45OTk4IDcuMDk5OEg3LjA5OThaIg0KICAgIGZpbGw9IiNkYzI2MjYiDQogID48L3BhdGg+DQo8L3N2Zz4=);
    position: absolute;
    top: 2.5px;
    left: 0;
  }
p[id^=error_] {
  margin-top: 0.25rem;
}
p[id^=error_] strong {
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  font-style: normal;
  --tw-text-opacity: 1;
  color: rgba(224, 36, 36, 1);
  color: rgba(224, 36, 36, var(--tw-text-opacity, 1));
}
select {
    border-radius: 100px !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
select {
    z-index: 20;
    position: relative;
  }
form select:focus {
  --tw-border-opacity: 1;
  border-color: rgba(98, 198, 197, 1);
  border-color: rgba(98, 198, 197, var(--tw-border-opacity, 1));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(98, 198, 197, var(--tw-ring-opacity, 1));
}
input[type=file] {
  margin-bottom: 0.5rem;
  display: block;
  width: 100%;
  border-radius: 100px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgba(208, 213, 221, 1);
  border-color: rgba(208, 213, 221, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, 1);
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgba(16, 24, 40, 1);
  color: rgba(16, 24, 40, var(--tw-text-opacity, 1));
}
input[type=file]:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.custom-tagsinput .tagsinput {
    width: 100% !important;
    height: auto !important;
  }
.custom-tagsinput .tagsinput {
    padding: 9px 9px 4px;
    border-radius: 8px;
    margin-bottom: 16px;
  }
.custom-tagsinput .ui-autocomplete-input:focus {
    outline: none;
  }
.custom-tagsinput .ui-autocomplete-input::-webkit-input-placeholder {
    color: #667085;
    font-size: 14px;
  }
.custom-tagsinput .ui-autocomplete-input::-moz-placeholder {
    color: #667085;
    font-size: 14px;
  }
.custom-tagsinput .ui-autocomplete-input::placeholder {
    color: #667085;
    font-size: 14px;
  }
.custom-tagsinput .tagsinput .tag {
    color: #8648d5;
    border-radius: 8px;
    background-color: #f9f6fe;
    padding: 4px 30px 4px 10px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
  }
.custom-tagsinput .tagsinput .tag a::after,
.custom-tagsinput .tagsinput .tag a::before {
    background-color: #8648d5;
  }
.custom-tagsinput .flex-container label {
    font-weight: 500 !important;
  }
.custom-tagsinput .flex-container label {
    font-size: 14px;
    display: block;
    margin-bottom: 16px;
  }
.public,
.dispute,
.dialog {
  --tw-bg-opacity: 1;
  background-color: rgba(252, 217, 189, 1);
  background-color: rgba(252, 217, 189, var(--tw-bg-opacity, 1));
}
.progress {
  --tw-bg-opacity: 1;
  background-color: rgba(167, 243, 208, 1);
  background-color: rgba(167, 243, 208, var(--tw-bg-opacity, 1));
}
.todo {
  --tw-bg-opacity: 1;
  background-color: rgba(205, 219, 254, 1);
  background-color: rgba(205, 219, 254, var(--tw-bg-opacity, 1));
}
.new,
span[class*=_proposal] {
  --tw-bg-opacity: 1;
  background-color: rgba(165, 243, 252, 1);
  background-color: rgba(165, 243, 252, var(--tw-bg-opacity, 1));
}
.proposal,
.starting {
  --tw-bg-opacity: 1;
  background-color: rgba(225, 207, 255, 1);
  background-color: rgba(225, 207, 255, var(--tw-bg-opacity, 1));
}
.complete,
.done {
  --tw-bg-opacity: 1;
  background-color: rgba(252, 233, 106, 1);
  background-color: rgba(252, 233, 106, var(--tw-bg-opacity, 1));
}
.draft {
  --tw-bg-opacity: 1;
  background-color: rgba(234, 236, 240, 1);
  background-color: rgba(234, 236, 240, var(--tw-bg-opacity, 1));
}
.rejected {
  --tw-bg-opacity: 1;
  background-color: rgba(251, 213, 213, 1);
  background-color: rgba(251, 213, 213, var(--tw-bg-opacity, 1));
}
.canceled {
  --tw-bg-opacity: 1;
  background-color: rgba(251, 213, 213, 1);
  background-color: rgba(251, 213, 213, var(--tw-bg-opacity, 1));
}
.success {
  --tw-bg-opacity: 1;
  background-color: rgba(209, 250, 229, 1);
  background-color: rgba(209, 250, 229, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(5, 150, 105, 1);
  color: rgba(5, 150, 105, var(--tw-text-opacity, 1));
}
.pending {
  --tw-bg-opacity: 1;
  background-color: rgba(253, 246, 178, 1);
  background-color: rgba(253, 246, 178, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(159, 88, 10, 1);
  color: rgba(159, 88, 10, var(--tw-text-opacity, 1));
}
.failed {
  --tw-bg-opacity: 1;
  background-color: rgba(253, 232, 232, 1);
  background-color: rgba(253, 232, 232, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(224, 36, 36, 1);
  color: rgba(224, 36, 36, var(--tw-text-opacity, 1));
}
.my-content {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    -webkit-line-clamp: var(--read-more-line-clamp, 2);
  }
.divider-y .formset-divider {
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgba(208, 213, 221, 1);
  border-color: rgba(208, 213, 221, var(--tw-border-opacity, 1));
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.divider-y .new-form {
  margin-bottom: 1rem;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgba(208, 213, 221, 1);
  border-color: rgba(208, 213, 221, var(--tw-border-opacity, 1));
}
.form-milestones .divider-y .new-form {
  border-style: none;
}
.divider-y .new-form .formset-divider:nth-child(even) {
  margin-top: 0px;
  border-style: none;
}
.divider-y .new-form .checkbox-delete {
  display: none;
}
.scrollbar-w-2::-webkit-scrollbar {
    width: 0.25rem;
    height: 0.25rem;
  }
.scrollbar-track-blue-lighter::-webkit-scrollbar-track {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, 1);
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}
.scrollbar-thumb-blue::-webkit-scrollbar-thumb {
  --tw-bg-opacity: 1;
  background-color: rgba(234, 236, 240, 1);
  background-color: rgba(234, 236, 240, var(--tw-bg-opacity, 1));
}
.scrollbar-thumb-rounded::-webkit-scrollbar-thumb {
  border-radius: 0.25rem;
}
#chat .message-own .message-wrap {
  -webkit-box-pack: end;
          justify-content: flex-end;
}
#chat .message-own .avatar {
  display: none;
}
#chat .message-own .message-body {
  --tw-bg-opacity: 1;
  background-color: rgba(171, 228, 225, 1);
  background-color: rgba(171, 228, 225, var(--tw-bg-opacity, 1));
}
#chat .message-recieved .message-body {
  --tw-bg-opacity: 1;
  background-color: rgba(213, 242, 240, 1);
  background-color: rgba(213, 242, 240, var(--tw-bg-opacity, 1));
}
#chat .message-system .message-wrap {
  -webkit-box-pack: center;
          justify-content: center;
}
#chat .message-system .message-wrap > div {
  width: 100%;
  max-width: 500px;
}
#chat .message-system .message-body {
  padding: 0px;
}
.show {
    display: block;
  }
.remove {
    display: none;
  }
.form-error {
  margin-bottom: 0.75rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgba(224, 36, 36, 1);
  color: rgba(224, 36, 36, var(--tw-text-opacity, 1));
}
input[type=radio] {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
    display: inline-block;
    width: 16px;
    height: 16px;
    padding: 4px;
    background-clip: content-box;
    border: 1px solid #d4d4d8;
    border-radius: 50%;
    background-color: #f3f4f6;
    color: transparent;
  }
input[type=radio]:checked,
input[type=radio]:focus {
    border: 5px solid #62c6c5 !important;
  }
input[type=radio]:checked,
input[type=radio]:focus {
    background-color: transparent;
    background-image: none;
    width: 16px;
    height: 16px;
    padding: 2px;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
.list-item-wrap {
  position: relative;
  margin-top: 0.75rem;
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgba(98, 198, 197, 1);
  border-color: rgba(98, 198, 197, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, 1);
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (min-width: 640px) {

  .list-item-wrap {
    width: calc(50% - 16px);
  }
}
.file-delete {
  position: absolute;
  top: 0.5rem;
  right: 1rem;
  cursor: pointer;
}
.my-content.long {
    -webkit-line-clamp: 4;
    -webkit-line-clamp: var(--read-more-line-clamp, 4);
  }
div[modal-backdrop] {
  z-index: 101;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-right-1 {
  right: -0.25rem;
}
.-right-3 {
  right: -0.75rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-2 {
  top: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-\[42px\] {
  bottom: 42px;
}
.left-0 {
  left: 0px;
}
.left-5 {
  left: 1.25rem;
}
.left-\[50\%\] {
  left: 50%;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-5 {
  right: 1.25rem;
}
.right-8 {
  right: 2rem;
}
.top-0 {
  top: 0px;
}
.top-1\.5 {
  top: 0.375rem;
}
.top-2 {
  top: 0.5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.top-6 {
  top: 1.5rem;
}
.top-8 {
  top: 2rem;
}
.top-\[15px\] {
  top: 15px;
}
.top-\[2px\] {
  top: 2px;
}
.top-\[3px\] {
  top: 3px;
}
.top-\[50\%\] {
  top: 50%;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[100\] {
  z-index: 100;
}
.z-\[101\] {
  z-index: 101;
}
.z-\[102\] {
  z-index: 102;
}
.z-\[1\] {
  z-index: 1;
}
.z-\[2\] {
  z-index: 2;
}
.z-\[30\] {
  z-index: 30;
}
.z-\[999\] {
  z-index: 999;
}
.order-1 {
  -webkit-box-ordinal-group: 2;
          order: 1;
}
.order-2 {
  -webkit-box-ordinal-group: 3;
          order: 2;
}
.order-3 {
  -webkit-box-ordinal-group: 4;
          order: 3;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.mx-1\.5 {
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.-mb-\[1px\] {
  margin-bottom: -1px;
}
.-mb-\[2px\] {
  margin-bottom: -2px;
}
.-mb-px {
  margin-bottom: -1px;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.-mt-2 {
  margin-top: -0.5rem;
}
.-mt-\[18px\] {
  margin-top: -18px;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-\[5px\] {
  margin-bottom: 5px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-8 {
  margin-left: 2rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-2\.5 {
  margin-right: 0.625rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-5 {
  margin-right: 1.25rem;
}
.mr-6 {
  margin-right: 1.5rem;
}
.mr-8 {
  margin-right: 2rem;
}
[dir="ltr"] .ms-1 {
  margin-left: 0.25rem;
}
[dir="rtl"] .ms-1 {
  margin-right: 0.25rem;
}
[dir="ltr"] .ms-2 {
  margin-left: 0.5rem;
}
[dir="rtl"] .ms-2 {
  margin-right: 0.5rem;
}
[dir="ltr"] .ms-auto {
  margin-left: auto;
}
[dir="rtl"] .ms-auto {
  margin-right: auto;
}
.mt-0 {
  margin-top: 0px;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-\[2px\] {
  margin-top: 2px;
}
.mt-\[3px\] {
  margin-top: 3px;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: -webkit-box;
  display: flex;
}
.inline-flex {
  display: -webkit-inline-box;
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.\!hidden {
  display: none !important;
}
.hidden {
  display: none;
}
.h-0 {
  height: 0px;
}
.h-1 {
  height: 0.25rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[100px\] {
  height: 100px;
}
.h-\[109px\] {
  height: 109px;
}
.h-\[180px\] {
  height: 180px;
}
.h-\[1px\] {
  height: 1px;
}
.h-\[2px\] {
  height: 2px;
}
.h-\[30px\] {
  height: 30px;
}
.h-\[33px\] {
  height: 33px;
}
.h-\[340px\] {
  height: 340px;
}
.h-\[6px\] {
  height: 6px;
}
.h-\[72px\] {
  height: 72px;
}
.h-\[80px\] {
  height: 80px;
}
.h-\[84px\] {
  height: 84px;
}
.h-\[90px\] {
  height: 90px;
}
.h-\[96px\] {
  height: 96px;
}
.h-\[calc\(100\%-1rem\)\] {
  height: calc(100% - 1rem);
}
.h-\[calc\(100vh-100px\)\] {
  height: calc(100vh - 100px);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-screen {
  height: 100vh;
}
.max-h-full {
  max-height: 100%;
}
.min-h-\[72px\] {
  min-height: 72px;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-\[calc\(100vh-56px\)\] {
  min-height: calc(100vh - 56px);
}
.min-h-\[calc\(100vh-72px\)\] {
  min-height: calc(100vh - 72px);
}
.\!w-\[1px\] {
  width: 1px !important;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-1\/2 {
  width: 50%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-2 {
  width: 0.5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-44 {
  width: 11rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[100px\] {
  width: 100px;
}
.w-\[109px\] {
  width: 109px;
}
.w-\[140px\] {
  width: 140px;
}
.w-\[180px\] {
  width: 180px;
}
.w-\[1px\] {
  width: 1px;
}
.w-\[200px\] {
  width: 200px;
}
.w-\[250px\] {
  width: 250px;
}
.w-\[30px\] {
  width: 30px;
}
.w-\[320px\] {
  width: 320px;
}
.w-\[6px\] {
  width: 6px;
}
.w-\[72px\] {
  width: 72px;
}
.w-\[80px\] {
  width: 80px;
}
.w-\[84px\] {
  width: 84px;
}
.w-\[90px\] {
  width: 90px;
}
.w-\[calc\(100\%-176px\)\] {
  width: calc(100% - 176px);
}
.w-\[calc\(100\%-20px\)\] {
  width: calc(100% - 20px);
}
.w-\[calc\(100\%-32px\)\] {
  width: calc(100% - 32px);
}
.w-\[calc\(100\%-40px\)\] {
  width: calc(100% - 40px);
}
.w-\[calc\(100\%-46px\)\] {
  width: calc(100% - 46px);
}
.w-\[calc\(100\%-52px\)\] {
  width: calc(100% - 52px);
}
.w-\[calc\(100\%-60px\)\] {
  width: calc(100% - 60px);
}
.w-\[calc\(100\%-80px\)\] {
  width: calc(100% - 80px);
}
.w-\[calc\(100vw\*2\)\] {
  width: calc(100vw * 2);
}
.w-\[calc\(50\%-10px\)\] {
  width: calc(50% - 10px);
}
.w-auto {
  width: auto;
}
.w-fit {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-screen {
  width: 100vw;
}
.min-w-\[240px\] {
  min-width: 240px;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-\[180px\] {
  max-width: 180px;
}
.max-w-\[260px\] {
  max-width: 260px;
}
.max-w-\[380px\] {
  max-width: 380px;
}
.max-w-\[416px\] {
  max-width: 416px;
}
.max-w-\[420px\] {
  max-width: 420px;
}
.max-w-\[500px\] {
  max-width: 500px;
}
.max-w-\[540px\] {
  max-width: 540px;
}
.max-w-\[680px\] {
  max-width: 680px;
}
.max-w-\[760px\] {
  max-width: 760px;
}
.max-w-\[800px\] {
  max-width: 800px;
}
.max-w-\[920px\] {
  max-width: 920px;
}
.max-w-\[calc\(100\%-80px\)\] {
  max-width: calc(100% - 80px);
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-screen-2xl {
  max-width: 1536px;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow-0 {
  -webkit-box-flex: 0;
          flex-grow: 0;
}
.table-auto {
  table-layout: auto;
}
.-translate-x-\[100vw\] {
  --tw-translate-x: -100vw;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-\[50\%\] {
  --tw-translate-x: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
  --tw-translate-x: -100%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-\[50\%\] {
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[200\%\] {
  --tw-translate-y: 200%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@-webkit-keyframes spin {

  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes spin {

  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.animate-spin {
  -webkit-animation: spin 1s linear infinite;
          animation: spin 1s linear infinite;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-decimal {
  list-style-type: decimal;
}
.list-none {
  list-style-type: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.flex-row {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
}
.flex-col {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.items-start {
  -webkit-box-align: start;
          align-items: flex-start;
}
.items-end {
  -webkit-box-align: end;
          align-items: flex-end;
}
.items-center {
  -webkit-box-align: center;
          align-items: center;
}
.items-stretch {
  -webkit-box-align: stretch;
          align-items: stretch;
}
.justify-start {
  -webkit-box-pack: start;
          justify-content: flex-start;
}
.justify-end {
  -webkit-box-pack: end;
          justify-content: flex-end;
}
.justify-center {
  -webkit-box-pack: center;
          justify-content: center;
}
.justify-between {
  -webkit-box-pack: justify;
          justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-\[2px\] {
  gap: 2px;
}
.gap-x-2 {
  -webkit-column-gap: 0.5rem;
     -moz-column-gap: 0.5rem;
          column-gap: 0.5rem;
}
.gap-x-3 {
  -webkit-column-gap: 0.75rem;
     -moz-column-gap: 0.75rem;
          column-gap: 0.75rem;
}
.gap-x-4 {
  -webkit-column-gap: 1rem;
     -moz-column-gap: 1rem;
          column-gap: 1rem;
}
.gap-x-5 {
  -webkit-column-gap: 1.25rem;
     -moz-column-gap: 1.25rem;
          column-gap: 1.25rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * (1 - var(--tw-space-x-reverse)));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * (1 - var(--tw-space-x-reverse)));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * (1 - var(--tw-space-x-reverse)));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-\[3px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(3px * var(--tw-space-x-reverse));
  margin-left: calc(3px * (1 - var(--tw-space-x-reverse)));
  margin-left: calc(3px * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * (1 - var(--tw-space-y-reverse)));
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * (1 - var(--tw-space-y-reverse)));
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * (1 - var(--tw-space-y-reverse)));
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * (1 - var(--tw-space-y-reverse)));
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * (1 - var(--tw-space-y-reverse)));
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * (1 - var(--tw-space-y-reverse)));
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * (1 - var(--tw-space-y-reverse)));
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * (1 - var(--tw-divide-y-reverse)));
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgba(242, 244, 247, 1);
  border-color: rgba(242, 244, 247, var(--tw-divide-opacity, 1));
}
.self-start {
  align-self: flex-start;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-y-scroll {
  overflow-y: scroll;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-normal {
  white-space: normal;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.break-words {
  word-wrap: break-word;
}
.\!rounded-\[100px\] {
  border-radius: 100px !important;
}
.\!rounded-\[20px\] {
  border-radius: 20px !important;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-\[100px\] {
  border-radius: 100px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
[dir="ltr"] .rounded-e-lg {
  border-top-right-radius: 0.5rem;
}
[dir="rtl"] .rounded-e-lg {
  border-top-left-radius: 0.5rem;
}
[dir="ltr"] .rounded-e-lg {
  border-bottom-right-radius: 0.5rem;
}
[dir="rtl"] .rounded-e-lg {
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rounded-r-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
[dir="ltr"] .rounded-s-lg {
  border-top-left-radius: 0.5rem;
}
[dir="rtl"] .rounded-s-lg {
  border-top-right-radius: 0.5rem;
}
[dir="ltr"] .rounded-s-lg {
  border-bottom-left-radius: 0.5rem;
}
[dir="rtl"] .rounded-s-lg {
  border-bottom-right-radius: 0.5rem;
}
.rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-\[4px\] {
  border-width: 4px;
}
.border-x {
  border-left-width: 1px;
  border-right-width: 1px;
}
.border-x-2 {
  border-left-width: 2px;
  border-right-width: 2px;
}
.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-0 {
  border-bottom-width: 0px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-\[1px\] {
  border-bottom-width: 1px;
}
.border-b-\[2px\] {
  border-bottom-width: 2px;
}
.border-l-0 {
  border-left-width: 0px;
}
.border-l-\[3px\] {
  border-left-width: 3px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-0 {
  border-right-width: 0px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-0 {
  border-top-width: 0px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-dashed {
  border-style: dashed;
}
.border-downy-300 {
  --tw-border-opacity: 1;
  border-color: rgba(98, 198, 197, 1);
  border-color: rgba(98, 198, 197, var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgba(234, 236, 240, 1);
  border-color: rgba(234, 236, 240, var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgba(208, 213, 221, 1);
  border-color: rgba(208, 213, 221, var(--tw-border-opacity, 1));
}
.border-gray-300\/50 {
  border-color: rgba(208, 213, 221, 0.5);
}
.border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgba(104, 117, 245, 1);
  border-color: rgba(104, 117, 245, var(--tw-border-opacity, 1));
}
.border-purple-800 {
  --tw-border-opacity: 1;
  border-color: rgba(123, 5, 210, 1);
  border-color: rgba(123, 5, 210, var(--tw-border-opacity, 1));
}
.border-purple-900 {
  --tw-border-opacity: 1;
  border-color: rgba(89, 6, 150, 1);
  border-color: rgba(89, 6, 150, var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgba(249, 128, 128, 1);
  border-color: rgba(249, 128, 128, var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgba(240, 82, 82, 1);
  border-color: rgba(240, 82, 82, var(--tw-border-opacity, 1));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgba(224, 36, 36, 1);
  border-color: rgba(224, 36, 36, var(--tw-border-opacity, 1));
}
.border-rose-600 {
  --tw-border-opacity: 1;
  border-color: rgba(239, 19, 152, 1);
  border-color: rgba(239, 19, 152, var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgba(255, 255, 255, 1);
  border-color: rgba(255, 255, 255, var(--tw-border-opacity, 1));
}
.\!bg-gray-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgba(249, 250, 251, 1) !important;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1)) !important;
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(245, 158, 11, 1);
  background-color: rgba(245, 158, 11, var(--tw-bg-opacity, 1));
}
.bg-black\/30 {
  background-color: rgba(0, 0, 0, 0.3);
}
.bg-black\/60 {
  background-color: rgba(0, 0, 0, 0.6);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(225, 239, 254, 1);
  background-color: rgba(225, 239, 254, var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(235, 245, 255, 1);
  background-color: rgba(235, 245, 255, var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(63, 131, 248, 1);
  background-color: rgba(63, 131, 248, var(--tw-bg-opacity, 1));
}
.bg-downy-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(213, 242, 240, 1);
  background-color: rgba(213, 242, 240, var(--tw-bg-opacity, 1));
}
.bg-downy-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(171, 228, 225, 1);
  background-color: rgba(171, 228, 225, var(--tw-bg-opacity, 1));
}
.bg-downy-300 {
  --tw-bg-opacity: 1;
  background-color: rgba(98, 198, 197, 1);
  background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
}
.bg-downy-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 250, 250, 1);
  background-color: rgba(243, 250, 250, var(--tw-bg-opacity, 1));
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(16, 185, 129, 1);
  background-color: rgba(16, 185, 129, var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(242, 244, 247, 1);
  background-color: rgba(242, 244, 247, var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(234, 236, 240, 1);
  background-color: rgba(234, 236, 240, var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgba(208, 213, 221, 1);
  background-color: rgba(208, 213, 221, var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgba(152, 162, 179, 1);
  background-color: rgba(152, 162, 179, var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, 1);
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(102, 112, 133, 1);
  background-color: rgba(102, 112, 133, var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgba(16, 24, 40, 1);
  background-color: rgba(16, 24, 40, var(--tw-bg-opacity, 1));
}
.bg-gray-900\/50 {
  background-color: rgba(16, 24, 40, 0.5);
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(222, 247, 236, 1);
  background-color: rgba(222, 247, 236, var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 250, 247, 1);
  background-color: rgba(243, 250, 247, var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(14, 159, 110, 1);
  background-color: rgba(14, 159, 110, var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 248, 241, 1);
  background-color: rgba(255, 248, 241, var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(247, 241, 255, 1);
  background-color: rgba(247, 241, 255, var(--tw-bg-opacity, 1));
}
.bg-purple-800 {
  --tw-bg-opacity: 1;
  background-color: rgba(123, 5, 210, 1);
  background-color: rgba(123, 5, 210, var(--tw-bg-opacity, 1));
}
.bg-purple-900 {
  --tw-bg-opacity: 1;
  background-color: rgba(89, 6, 150, 1);
  background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(253, 232, 232, 1);
  background-color: rgba(253, 232, 232, var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(253, 242, 242, 1);
  background-color: rgba(253, 242, 242, var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(240, 82, 82, 1);
  background-color: rgba(240, 82, 82, var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 36, 36, 1);
  background-color: rgba(224, 36, 36, var(--tw-bg-opacity, 1));
}
.bg-rose-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(239, 19, 152, 1);
  background-color: rgba(239, 19, 152, var(--tw-bg-opacity, 1));
}
.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(14, 165, 233, 1);
  background-color: rgba(14, 165, 233, var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, 1);
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
}
.bg-white\/50 {
  background-color: rgba(255, 255, 255, 0.5);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(253, 246, 178, 1);
  background-color: rgba(253, 246, 178, var(--tw-bg-opacity, 1));
}
.bg-gradient-to-b {
  background-image: -webkit-gradient(linear, left top, left bottom, from(var(--tw-gradient-stops)));
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-icon-send {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAUCAYAAACJfM0wAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIlSURBVHgBrZRfkppAEMa7B6LGvJATxL1Bdt9AU+oJsjmBEPOuNwi5gfseI97AnEBSu0Buse4JQt7W3TCdHgwoRiz2T1fBDFPDr3u+b2YAOOzwcuT8DN7DMwY60dUACT31QQArQHJnZmcOTwxdkDBIIRmKAC3gJE4YuDy20Agn03b7Bh4RIlmv55tqaSVRnACCh0SGABwTwupjcDUbBsEbeGCgejlRuGRYT67vXnv9fmxHUUsD6BJJF9QqNrEgwMnMsn5UBg/DcMQVT5CrnFrWxe6EYRQNdhNU9SEF28ulIeq1Xwz3Z1anf2jip+iyRyS4ADjfTSAk+od8wKyzL0dZJUomAfIzk+00AVHMkAWv1t1NIPIOwUK1Wr0+gCPhmebqm9l2UqOVfIgxINr7RmOhEpLXqgJQk4vB3zguM67gA/8v7+5P8orh9jbX5sBj6ES/D0GVPwn9MWhvXM862HjRVY4gCnd/Z5QBoVYbIdCY9TVSBqJPmj722p1Y304Vdup1o7E4BnTCsIsgz4nNQwSDf2HpwJWN5oV3ehoXKubsLbUjOLs/PTu7KQMCSJfn9JQ43G6AL18VgAVwJgOg5v0H5EtKba0cyMvl1pta5tEDohdkqNf9fyvY6kdb/RCSL1/Ndz5UCFQy8Km7VjIk6/sPORAZqLaeJjxByfeqwLziTAZJ8FYd63Q0M6REv0rg7D5OK0zvZJwkzeb8scBCqJOjLhl4xvgLVKcb+dqWnHUAAAAASUVORK5CYII=);
}
.bg-onboarding {
  background-image: url(http://localhost:9091/frontend/vendors/images/cover-photo.jpg);
}
.bg-sign-in {
  background-image: url(http://localhost:9091/frontend/vendors/images/sign-in-photo.jpg);
}
.from-black\/\[0\.81\] {
  --tw-gradient-from: rgba(0, 0, 0, 0.81) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(0, 0, 0, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-black\/0 {
  --tw-gradient-to: rgba(0, 0, 0, 0) var(--tw-gradient-to-position);
}
.bg-cover {
  background-size: cover;
}
.bg-center {
  background-position: center;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.fill-blue-600 {
  fill: #1C64F2;
}
.fill-current {
  fill: currentColor;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[11px\] {
  padding-left: 11px;
  padding-right: 11px;
}
.px-\[48px\] {
  padding-left: 48px;
  padding-right: 48px;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px;
}
.py-\[52px\] {
  padding-top: 52px;
  padding-bottom: 52px;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-10 {
  padding-bottom: 2.5rem;
}
.pb-12 {
  padding-bottom: 3rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pb-\[60\%\] {
  padding-bottom: 60%;
}
.pb-\[75\%\] {
  padding-bottom: 75%;
}
.pl-0 {
  padding-left: 0px;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-9 {
  padding-left: 2.25rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-14 {
  padding-right: 3.5rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-24 {
  padding-right: 6rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-40 {
  padding-right: 10rem;
}
.pr-\[140px\] {
  padding-right: 140px;
}
.pt-0 {
  padding-top: 0px;
}
.pt-12 {
  padding-top: 3rem;
}
.pt-14 {
  padding-top: 3.5rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.pt-\[100px\] {
  padding-top: 100px;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.align-middle {
  vertical-align: middle;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[28px\] {
  font-size: 28px;
}
.text-\[30px\] {
  font-size: 30px;
}
.text-\[32px\] {
  font-size: 32px;
}
.text-\[36px\] {
  font-size: 36px;
}
.text-\[44px\] {
  font-size: 44px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.italic {
  font-style: italic;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-9 {
  line-height: 2.25rem;
}
.leading-\[28px\] {
  line-height: 28px;
}
.leading-\[30px\] {
  line-height: 30px;
}
.leading-\[36px\] {
  line-height: 36px;
}
.leading-\[50px\] {
  line-height: 50px;
}
.leading-none {
  line-height: 1;
}
.leading-normal {
  line-height: 1.5;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgba(26, 86, 219, 1);
  color: rgba(26, 86, 219, var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgba(30, 66, 159, 1);
  color: rgba(30, 66, 159, var(--tw-text-opacity, 1));
}
.text-downy-300 {
  --tw-text-opacity: 1;
  color: rgba(98, 198, 197, 1);
  color: rgba(98, 198, 197, var(--tw-text-opacity, 1));
}
.text-downy-500 {
  --tw-text-opacity: 1;
  color: rgba(52, 148, 152, 1);
  color: rgba(52, 148, 152, var(--tw-text-opacity, 1));
}
.text-gold {
  --tw-text-opacity: 1;
  color: rgba(248, 221, 23, 1);
  color: rgba(248, 221, 23, var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgba(234, 236, 240, 1);
  color: rgba(234, 236, 240, var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgba(208, 213, 221, 1);
  color: rgba(208, 213, 221, var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgba(152, 162, 179, 1);
  color: rgba(152, 162, 179, var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgba(102, 112, 133, 1);
  color: rgba(102, 112, 133, var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgba(71, 84, 103, 1);
  color: rgba(71, 84, 103, var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgba(52, 64, 84, 1);
  color: rgba(52, 64, 84, var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgba(29, 41, 57, 1);
  color: rgba(29, 41, 57, var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgba(16, 24, 40, 1);
  color: rgba(16, 24, 40, var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgba(4, 108, 78, 1);
  color: rgba(4, 108, 78, var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgba(3, 84, 63, 1);
  color: rgba(3, 84, 63, var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgba(88, 80, 236, 1);
  color: rgba(88, 80, 236, var(--tw-text-opacity, 1));
}
.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgba(180, 52, 3, 1);
  color: rgba(180, 52, 3, var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgba(157, 25, 255, 1);
  color: rgba(157, 25, 255, var(--tw-text-opacity, 1));
}
.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgba(89, 6, 150, 1);
  color: rgba(89, 6, 150, var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgba(240, 82, 82, 1);
  color: rgba(240, 82, 82, var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgba(224, 36, 36, 1);
  color: rgba(224, 36, 36, var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgba(200, 30, 30, 1);
  color: rgba(200, 30, 30, var(--tw-text-opacity, 1));
}
.text-transparent {
  color: transparent;
}
.text-white {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, 1);
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}
.text-white\/60 {
  color: rgba(255, 255, 255, 0.6);
}
.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgba(250, 202, 21, 1);
  color: rgba(250, 202, 21, var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgba(114, 59, 19, 1);
  color: rgba(114, 59, 19, var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.decoration-1 {
  text-decoration-thickness: 1px;
}
.underline-offset-2 {
  text-underline-offset: 2px;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  -webkit-box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
          box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
  -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  -webkit-box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
          box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
  -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  -webkit-box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
          box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
  -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  -webkit-box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
          box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), var(--tw-shadow);
  -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.filter {
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.\!transition-none {
  -webkit-transition-property: none !important;
  transition-property: none !important;
}
.transition-all {
  -webkit-transition-property: all;
  transition-property: all;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
          transition-duration: 150ms;
}
.transition-transform {
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
          transition-duration: 150ms;
}
.duration-200 {
  -webkit-transition-duration: 200ms;
          transition-duration: 200ms;
}
.ease-in {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
@media (min-width: 640px) {

  .sm\:purple.btn-primary::before {
    --tw-bg-opacity: 1;
    background-color: rgba(89, 6, 150, 1);
    background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
  }

  .sm\:downy.btn-primary::before {
    --tw-bg-opacity: 1;
    background-color: rgba(98, 198, 197, 1);
    background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
  }
}
.before\:w-\[3px\]::before {
  content: var(--tw-content);
  width: 3px;
}
.before\:bg-transparent::before {
  content: var(--tw-content);
  background-color: transparent;
}
.after\:mx-2::after {
  content: var(--tw-content);
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.after\:mx-6::after {
  content: var(--tw-content);
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.after\:hidden::after {
  content: var(--tw-content);
  display: none;
}
.after\:h-10::after {
  content: var(--tw-content);
  height: 2.5rem;
}
.after\:h-\[2px\]::after {
  content: var(--tw-content);
  height: 2px;
}
.after\:\!w-10::after {
  width: 2.5rem !important;
}
.after\:\!w-10::after {
  content: var(--tw-content);
}
.after\:w-12::after {
  content: var(--tw-content);
  width: 3rem;
}
.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:border-b::after {
  content: var(--tw-content);
  border-bottom-width: 1px;
}
.after\:border-downy-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgba(98, 198, 197, 1);
  border-color: rgba(98, 198, 197, var(--tw-border-opacity, 1));
}
.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgba(208, 213, 221, 1);
  border-color: rgba(208, 213, 221, var(--tw-border-opacity, 1));
}
.after\:bg-downy-300::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgba(98, 198, 197, 1);
  background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
}
.after\:text-sm::after {
  content: var(--tw-content);
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.after\:font-light::after {
  content: var(--tw-content);
  font-weight: 300;
}
.after\:leading-10::after {
  content: var(--tw-content);
  line-height: 2.5rem;
}
.after\:text-downy-300::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgba(98, 198, 197, 1);
  color: rgba(98, 198, 197, var(--tw-text-opacity, 1));
}
.after\:text-gray-200::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgba(234, 236, 240, 1);
  color: rgba(234, 236, 240, var(--tw-text-opacity, 1));
}
.after\:text-gray-300::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgba(208, 213, 221, 1);
  color: rgba(208, 213, 221, var(--tw-text-opacity, 1));
}
.after\:text-white::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, 1);
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}
.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}
.after\:content-\[\'\/\'\]::after {
  --tw-content: '/';
  content: var(--tw-content);
}
.after\:content-\[\'next\'\]::after {
  --tw-content: 'next';
  content: var(--tw-content);
}
.after\:content-\[\'prev\'\]::after {
  --tw-content: 'prev';
  content: var(--tw-content);
}
.hover\:border-downy-300:hover {
  --tw-border-opacity: 1;
  border-color: rgba(98, 198, 197, 1);
  border-color: rgba(98, 198, 197, var(--tw-border-opacity, 1));
}
.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgba(208, 213, 221, 1);
  border-color: rgba(208, 213, 221, var(--tw-border-opacity, 1));
}
.hover\:border-purple-800:hover {
  --tw-border-opacity: 1;
  border-color: rgba(123, 5, 210, 1);
  border-color: rgba(123, 5, 210, var(--tw-border-opacity, 1));
}
.hover\:border-red-700:hover {
  --tw-border-opacity: 1;
  border-color: rgba(200, 30, 30, 1);
  border-color: rgba(200, 30, 30, var(--tw-border-opacity, 1));
}
.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(26, 86, 219, 1);
  background-color: rgba(26, 86, 219, var(--tw-bg-opacity, 1));
}
.hover\:bg-downy-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(98, 198, 197, 1);
  background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(242, 244, 247, 1);
  background-color: rgba(242, 244, 247, var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(234, 236, 240, 1);
  background-color: rgba(234, 236, 240, var(--tw-bg-opacity, 1));
}
.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(4, 108, 78, 1);
  background-color: rgba(4, 108, 78, var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(123, 5, 210, 1);
  background-color: rgba(123, 5, 210, var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(89, 6, 150, 1);
  background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
}
.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 36, 36, 1);
  background-color: rgba(224, 36, 36, var(--tw-bg-opacity, 1));
}
.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(200, 30, 30, 1);
  background-color: rgba(200, 30, 30, var(--tw-bg-opacity, 1));
}
.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, 1);
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
}
.hover\:text-downy-300:hover {
  --tw-text-opacity: 1;
  color: rgba(98, 198, 197, 1);
  color: rgba(98, 198, 197, var(--tw-text-opacity, 1));
}
.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgba(102, 112, 133, 1);
  color: rgba(102, 112, 133, var(--tw-text-opacity, 1));
}
.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgba(71, 84, 103, 1);
  color: rgba(71, 84, 103, var(--tw-text-opacity, 1));
}
.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgba(52, 64, 84, 1);
  color: rgba(52, 64, 84, var(--tw-text-opacity, 1));
}
.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgba(16, 24, 40, 1);
  color: rgba(16, 24, 40, var(--tw-text-opacity, 1));
}
.hover\:text-purple-800:hover {
  --tw-text-opacity: 1;
  color: rgba(123, 5, 210, 1);
  color: rgba(123, 5, 210, var(--tw-text-opacity, 1));
}
.hover\:text-purple-900:hover {
  --tw-text-opacity: 1;
  color: rgba(89, 6, 150, 1);
  color: rgba(89, 6, 150, var(--tw-text-opacity, 1));
}
.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, 1);
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:no-underline:hover {
  text-decoration-line: none;
}
.hover\:underline-offset-2:hover {
  text-underline-offset: 2px;
}
.hover\:after\:bg-downy-400:hover::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgba(78, 177, 179, 1);
  background-color: rgba(78, 177, 179, var(--tw-bg-opacity, 1));
}
.focus\:border-downy-300:focus {
  --tw-border-opacity: 1;
  border-color: rgba(98, 198, 197, 1);
  border-color: rgba(98, 198, 197, var(--tw-border-opacity, 1));
}
.focus\:bg-purple-900:focus {
  --tw-bg-opacity: 1;
  background-color: rgba(89, 6, 150, 1);
  background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:outline:focus {
  outline-style: solid;
}
.focus\:outline-0:focus {
  outline-width: 0px;
}
.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
}
.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 0 rgba(0,0,0,0);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
          box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0,0,0,0));
}
.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(63, 131, 248, var(--tw-ring-opacity, 1));
}
.focus\:ring-downy-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(98, 198, 197, var(--tw-ring-opacity, 1));
}
.focus\:ring-gray-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(234, 236, 240, var(--tw-ring-opacity, 1));
}
.active\:bg-purple-900:active {
  --tw-bg-opacity: 1;
  background-color: rgba(89, 6, 150, 1);
  background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
}
.active\:bg-red-500:active {
  --tw-bg-opacity: 1;
  background-color: rgba(240, 82, 82, 1);
  background-color: rgba(240, 82, 82, var(--tw-bg-opacity, 1));
}
.active\:after\:bg-downy-300:active::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgba(98, 198, 197, 1);
  background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:visible {
  visibility: visible;
}
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
.dark *.dark\:\!bg-gray-700 {
  --tw-bg-opacity: 1 !important;
  background-color: rgba(52, 64, 84, 1) !important;
  background-color: rgba(52, 64, 84, var(--tw-bg-opacity, 1)) !important;
}
.dark *.dark\:bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgba(152, 162, 179, 1);
  background-color: rgba(152, 162, 179, var(--tw-bg-opacity, 1));
}
.dark *.dark\:bg-gray-900\/80 {
  background-color: rgba(16, 24, 40, 0.8);
}
.dark *.dark\:text-gray-400 {
  --tw-text-opacity: 1;
  color: rgba(152, 162, 179, 1);
  color: rgba(152, 162, 179, var(--tw-text-opacity, 1));
}
.dark *.dark\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgba(102, 112, 133, 1);
  color: rgba(102, 112, 133, var(--tw-text-opacity, 1));
}
.dark *.dark\:text-gray-600 {
  --tw-text-opacity: 1;
  color: rgba(71, 84, 103, 1);
  color: rgba(71, 84, 103, var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .sm\:order-1 {
    -webkit-box-ordinal-group: 2;
            order: 1;
  }

  .sm\:order-2 {
    -webkit-box-ordinal-group: 3;
            order: 2;
  }

  .sm\:mx-3 {
    margin-left: 0.75rem;
    margin-right: 0.75rem;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .sm\:mb-8 {
    margin-bottom: 2rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: -webkit-box;
    display: flex;
  }

  .sm\:inline-flex {
    display: -webkit-inline-box;
    display: inline-flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-6 {
    height: 1.5rem;
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:w-36 {
    width: 9rem;
  }

  .sm\:w-40 {
    width: 10rem;
  }

  .sm\:w-44 {
    width: 11rem;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-52 {
    width: 13rem;
  }

  .sm\:w-56 {
    width: 14rem;
  }

  .sm\:w-6 {
    width: 1.5rem;
  }

  .sm\:w-72 {
    width: 18rem;
  }

  .sm\:w-\[160px\] {
    width: 160px;
  }

  .sm\:w-\[calc\(100\%-176px\)\] {
    width: calc(100% - 176px);
  }

  .sm\:w-\[calc\(100\%-46px\)\] {
    width: calc(100% - 46px);
  }

  .sm\:w-\[calc\(50\%-18px\)\] {
    width: calc(50% - 18px);
  }

  .sm\:w-\[calc\(50\%-6px\)\] {
    width: calc(50% - 6px);
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
            flex-direction: row;
  }

  .sm\:flex-col {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
  }

  .sm\:flex-wrap {
    flex-wrap: wrap;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .sm\:items-start {
    -webkit-box-align: start;
            align-items: flex-start;
  }

  .sm\:items-end {
    -webkit-box-align: end;
            align-items: flex-end;
  }

  .sm\:items-center {
    -webkit-box-align: center;
            align-items: center;
  }

  .sm\:justify-end {
    -webkit-box-pack: end;
            justify-content: flex-end;
  }

  .sm\:justify-between {
    -webkit-box-pack: justify;
            justify-content: space-between;
  }

  .sm\:gap-2 {
    gap: 0.5rem;
  }

  .sm\:gap-3 {
    gap: 0.75rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:gap-x-4 {
    -webkit-column-gap: 1rem;
       -moz-column-gap: 1rem;
            column-gap: 1rem;
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * (1 - var(--tw-space-x-reverse)));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * (1 - var(--tw-space-y-reverse)));
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
  }

  .sm\:bg-downy-300 {
    --tw-bg-opacity: 1;
    background-color: rgba(98, 198, 197, 1);
    background-color: rgba(98, 198, 197, var(--tw-bg-opacity, 1));
  }

  .sm\:bg-purple-900 {
    --tw-bg-opacity: 1;
    background-color: rgba(89, 6, 150, 1);
    background-color: rgba(89, 6, 150, var(--tw-bg-opacity, 1));
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
  }

  .sm\:py-3\.5 {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
  }

  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .sm\:pr-10 {
    padding-right: 2.5rem;
  }

  .sm\:pr-16 {
    padding-right: 4rem;
  }

  .sm\:pr-32 {
    padding-right: 8rem;
  }

  .sm\:pt-12 {
    padding-top: 3rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-right {
    text-align: right;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-white {
    --tw-text-opacity: 1;
    color: rgba(255, 255, 255, 1);
    color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
  }

  .sm\:after\:inline-block::after {
    content: var(--tw-content);
    display: inline-block;
  }

  .sm\:after\:hidden::after {
    content: var(--tw-content);
    display: none;
  }

  .sm\:after\:content-\[\'\'\]::after {
    --tw-content: '';
    content: var(--tw-content);
  }
}
@media (min-width: 768px) {

  .md\:static {
    position: static;
  }

  .md\:absolute {
    position: absolute;
  }

  .md\:inset-0 {
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
  }

  .md\:left-0 {
    left: 0px;
  }

  .md\:right-0 {
    right: 0px;
  }

  .md\:top-0 {
    top: 0px;
  }

  .md\:top-\[140px\] {
    top: 140px;
  }

  .md\:order-1 {
    -webkit-box-ordinal-group: 2;
            order: 1;
  }

  .md\:order-2 {
    -webkit-box-ordinal-group: 3;
            order: 2;
  }

  .md\:order-3 {
    -webkit-box-ordinal-group: 4;
            order: 3;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:-mt-2 {
    margin-top: -0.5rem;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-8 {
    margin-top: 2rem;
  }

  .md\:block {
    display: block;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[102px\] {
    height: 102px;
  }

  .md\:h-\[calc\(100vh-124px\)\] {
    height: calc(100vh - 124px);
  }

  .md\:w-32 {
    width: 8rem;
  }

  .md\:w-44 {
    width: 11rem;
  }

  .md\:w-5\/12 {
    width: 41.666667%;
  }

  .md\:w-6\/12 {
    width: 50%;
  }

  .md\:w-7\/12 {
    width: 58.333333%;
  }

  .md\:w-72 {
    width: 18rem;
  }

  .md\:w-\[102px\] {
    width: 102px;
  }

  .md\:w-\[300px\] {
    width: 300px;
  }

  .md\:w-\[400px\] {
    width: 400px;
  }

  .md\:w-\[42rem\] {
    width: 42rem;
  }

  .md\:w-\[calc\(100\%-144px\)\] {
    width: calc(100% - 144px);
  }

  .md\:w-\[calc\(100\%-46px\)\] {
    width: calc(100% - 46px);
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:max-w-\[300px\] {
    max-width: 300px;
  }

  .md\:shrink-0 {
    flex-shrink: 0;
  }

  .md\:flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
            flex-direction: row;
  }

  .md\:flex-col {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
            flex-direction: column;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-end {
    -webkit-box-align: end;
            align-items: flex-end;
  }

  .md\:items-center {
    -webkit-box-align: center;
            align-items: center;
  }

  .md\:justify-end {
    -webkit-box-pack: end;
            justify-content: flex-end;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:gap-x-8 {
    -webkit-column-gap: 2rem;
       -moz-column-gap: 2rem;
            column-gap: 2rem;
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * (1 - var(--tw-space-y-reverse)));
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * (1 - var(--tw-space-y-reverse)));
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .md\:rounded-\[100px\] {
    border-radius: 100px;
  }

  .md\:rounded-lg {
    border-radius: 0.5rem;
  }

  .md\:rounded-xl {
    border-radius: 0.75rem;
  }

  .md\:rounded-b-lg {
    border-bottom-right-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  .md\:rounded-t-lg {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
  }

  .md\:border {
    border-width: 1px;
  }

  .md\:border-x {
    border-left-width: 1px;
    border-right-width: 1px;
  }

  .md\:border-b {
    border-bottom-width: 1px;
  }

  .md\:border-b-0 {
    border-bottom-width: 0px;
  }

  .md\:border-b-\[1px\] {
    border-bottom-width: 1px;
  }

  .md\:p-3 {
    padding: 0.75rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-5 {
    padding: 1.25rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .md\:pb-16 {
    padding-bottom: 4rem;
  }

  .md\:pb-8 {
    padding-bottom: 2rem;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-12 {
    padding-left: 3rem;
  }

  .md\:pl-\[300px\] {
    padding-left: 300px;
  }

  .md\:pr-0 {
    padding-right: 0px;
  }

  .md\:pr-10 {
    padding-right: 2.5rem;
  }

  .md\:pr-20 {
    padding-right: 5rem;
  }

  .md\:pr-4 {
    padding-right: 1rem;
  }

  .md\:pr-40 {
    padding-right: 10rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-10 {
    padding-top: 2.5rem;
  }

  .md\:pt-16 {
    padding-top: 4rem;
  }

  .md\:pt-8 {
    padding-top: 2rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-right {
    text-align: right;
  }

  .md\:text-\[32px\] {
    font-size: 32px;
  }
}
@media (min-width: 1024px) {

  .lg\:absolute {
    position: absolute;
  }

  .lg\:top-\[2px\] {
    top: 2px;
  }

  .lg\:z-10 {
    z-index: 10;
  }

  .lg\:order-1 {
    -webkit-box-ordinal-group: 2;
            order: 1;
  }

  .lg\:order-2 {
    -webkit-box-ordinal-group: 3;
            order: 2;
  }

  .lg\:-ml-10 {
    margin-left: -2.5rem;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-1 {
    margin-top: 0.25rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline-block {
    display: inline-block;
  }

  .lg\:flex {
    display: -webkit-box;
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[calc\(100vh-156px\)\] {
    height: calc(100vh - 156px);
  }

  .lg\:h-screen {
    height: 100vh;
  }

  .lg\:min-h-\[calc\(100vh-100px\)\] {
    min-height: calc(100vh - 100px);
  }

  .lg\:min-h-\[calc\(100vh-62px\)\] {
    min-height: calc(100vh - 62px);
  }

  .lg\:min-h-\[calc\(100vh-72px\)\] {
    min-height: calc(100vh - 72px);
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-5\/12 {
    width: 41.666667%;
  }

  .lg\:w-7\/12 {
    width: 58.333333%;
  }

  .lg\:w-\[40\%\] {
    width: 40%;
  }

  .lg\:w-\[420px\] {
    width: 420px;
  }

  .lg\:w-\[60\%\] {
    width: 60%;
  }

  .lg\:w-\[calc\(100\%-320px\)\] {
    width: calc(100% - 320px);
  }

  .lg\:w-\[calc\(100\%-400px\)\] {
    width: calc(100% - 400px);
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:max-w-\[380px\] {
    max-width: 380px;
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0px;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
            flex-direction: row;
  }

  .lg\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .lg\:justify-end {
    -webkit-box-pack: end;
            justify-content: flex-end;
  }

  .lg\:justify-between {
    -webkit-box-pack: justify;
            justify-content: space-between;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * (1 - var(--tw-space-x-reverse)));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:border-0 {
    border-width: 0px;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .lg\:pb-8 {
    padding-bottom: 2rem;
  }

  .lg\:pl-\[250px\] {
    padding-left: 250px;
  }

  .lg\:pl-\[420px\] {
    padding-left: 420px;
  }

  .lg\:pr-0 {
    padding-right: 0px;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-12 {
    padding-top: 3rem;
  }

  .lg\:text-\[38px\] {
    font-size: 38px;
  }

  .lg\:before\:absolute::before {
    content: var(--tw-content);
    position: absolute;
  }

  .lg\:before\:h-full::before {
    content: var(--tw-content);
    height: 100%;
  }

  .lg\:before\:rounded::before {
    content: var(--tw-content);
    border-radius: 0.25rem;
  }

  .lg\:before\:bg-\[\#e6e9ec\]::before {
    content: var(--tw-content);
    --tw-bg-opacity: 1;
    background-color: rgba(230, 233, 236, 1);
    background-color: rgba(230, 233, 236, var(--tw-bg-opacity, 1));
  }

  .lg\:before\:content-\[\'\'\]::before {
    --tw-content: '';
    content: var(--tw-content);
  }
}
@media (min-width: 1280px) {

  .xl\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .xl\:w-3\/12 {
    width: 25%;
  }

  .xl\:w-4\/12 {
    width: 33.333333%;
  }

  .xl\:w-7\/12 {
    width: 58.333333%;
  }

  .xl\:w-8\/12 {
    width: 66.666667%;
  }

  .xl\:max-w-\[416px\] {
    max-width: 416px;
  }

  .xl\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .xl\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .xl\:text-\[42px\] {
    font-size: 42px;
  }

  .xl\:after\:mx-10::after {
    content: var(--tw-content);
    margin-left: 2.5rem;
    margin-right: 2.5rem;
  }
}
@media (min-width: 1536px) {

  .\32xl\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }
}

/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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*/