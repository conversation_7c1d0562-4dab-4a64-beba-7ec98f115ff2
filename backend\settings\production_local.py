"""
Production settings with local storage support.
This allows running in production with local file storage instead of S3.
"""
import os
from .base import *
from .local_storage import *

# INSTALLED_APPS += [
# ]

DEBUG = False

# redirects to www if it is not there
PREPEND_WWW = True

# Email settings - Use console backend for local development
# In production, you might want to configure SMTP or SES
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Security settings for production
ALLOWED_HOSTS = os.environ.get("ALLOWED_HOSTS", "localhost,127.0.0.1").split(",")

# reCAPTCHA configuration for testing/development
# Set SILENCE_RECAPTCHA_TEST_KEY_ERROR=true in .env to silence the test key warning
# This is useful when using Google's test keys for development/testing
SILENCE_RECAPTCHA_TEST_KEY_ERROR = os.environ.get("SILENCE_RECAPTCHA_TEST_KEY_ERROR", "false").lower() == "true"

if SILENCE_RECAPTCHA_TEST_KEY_ERROR:
    SILENCED_SYSTEM_CHECKS = getattr(globals(), 'SILENCED_SYSTEM_CHECKS', [])
    SILENCED_SYSTEM_CHECKS.append('django_recaptcha.recaptcha_test_key_error')

# HTTPS settings (uncomment for production with HTTPS)
# SECURE_SSL_REDIRECT = True
# SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True

# Override static file configuration for production deployment
# When using local storage in production, we need absolute URLs
if USE_LOCAL_STORAGE:
    # Get the domain from environment or use a default
    DEPLOYMENT_DOMAIN = os.environ.get("DEPLOYMENT_DOMAIN", DOMAIN)

    # Override STATIC_URL to use absolute URL with domain
    STATIC_URL = f"http://{DEPLOYMENT_DOMAIN}/static/"

    # Keep the same STATIC_ROOT as defined in local_storage.py
    # STATIC_ROOT is already set in local_storage.py
else:
    # Static files collection for production (S3 case)
    STATIC_ROOT = str(BASE_DIR.parent / "staticfiles")

# Webpack loader for production
WEBPACK_LOADER = {
    "MANIFEST_FILE": str(BASE_DIR.parent / "frontend/build/manifest.json"),
}

# Logging configuration for production
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "full_format": {
            "format": "%(asctime)s: %(levelname)s %(message)s [%(name)s:%(lineno)d]",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "full_format",
        },
        "file": {
            "class": "logging.FileHandler",
            "filename": str(BASE_DIR.parent / "logs" / "django.log"),
            "formatter": "full_format",
        },
    },
    "loggers": {
        "": {
            "level": "INFO",
            "handlers": ["console", "file"],
        },
        "finance_stripe": {
            "level": "DEBUG",
            "handlers": ["console", "file"],
            "propagate": False,
        },
    },
}

# Create logs directory if it doesn't exist
import os
logs_dir = BASE_DIR.parent / "logs"
os.makedirs(logs_dir, exist_ok=True)
