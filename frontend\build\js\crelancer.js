"use strict";
(self["webpackChunkpython_webpack_boilerplate"] = self["webpackChunkpython_webpack_boilerplate"] || []).push([["crelancer"],{

/***/ "./frontend/src/application/crelancer.js":
/*!***********************************************!*\
  !*** ./frontend/src/application/crelancer.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _styles_crelancer_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../styles/crelancer.scss */ "./frontend/src/styles/crelancer.scss");
/* harmony import */ var _hotwired_turbo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hotwired/turbo */ "./node_modules/@hotwired/turbo/dist/turbo.es2017-esm.js");
/* harmony import */ var flowbite__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! flowbite */ "./node_modules/flowbite/lib/esm/index.js");
/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @stripe/stripe-js */ "./node_modules/@stripe/stripe-js/dist/stripe.esm.js");
/* harmony import */ var _controllers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../controllers */ "./frontend/src/controllers/index.js");
/* harmony import */ var swiper_css_bundle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/css/bundle */ "./node_modules/swiper/swiper-bundle.min.css");
// This is the main website component

// This is the scss entry file







// We can import other JS file as we like
// import "../components/saide.js";
// require('webpack-jquery-ui/autocomplete');

window.document.addEventListener("DOMContentLoaded", function () {
  window.console.log("dom ready 1");
});
document.addEventListener('turbo:load', function () {
  // new
  console.log('turbo:load');
});

/*dispatch event for chat*/
let chatMessages = document.querySelector('#chat-messages');
if (chatMessages !== null) {
  chatMessages.addEventListener('turbo:frame-render', () => {
    const event = new Event('parent:frame-render');
    document.dispatchEvent(event);
  });
}
let chatdealsList = document.querySelector('#my-deals-list');
if (chatdealsList !== null) {
  chatdealsList.addEventListener('turbo:frame-load', () => {
    const event = new Event('parent:frame-load');
    document.dispatchEvent(event);
  });
}

/***/ }),

/***/ "./frontend/src/controllers/anchor_movement_controller.js":
/*!****************************************************************!*\
  !*** ./frontend/src/controllers/anchor_movement_controller.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {}
  scroll(e) {
    e.preventDefault();
    const targetId = e.target.getAttribute('href').substring(1);
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      const offset = targetElement.getBoundingClientRect().top + window.scrollY - 108;
      window.scrollTo({
        top: offset,
        behavior: 'smooth'
      });
    }
  }
  disconnect() {
    this.element.remove();
  }
}
_defineProperty(_Class, "targets", ["button"]);

/***/ }),

/***/ "./frontend/src/controllers/application.js":
/*!*************************************************!*\
  !*** ./frontend/src/controllers/application.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   application: () => (/* binding */ application)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
/* harmony import */ var tailwindcss_stimulus_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwindcss-stimulus-components */ "./node_modules/tailwindcss-stimulus-components/dist/tailwindcss-stimulus-components.module.js");


const application = _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Application.start();

// Configure Stimulus development experience
application.debug = false;
window.Stimulus = application;
window.Stimulus.register('alert', tailwindcss_stimulus_components__WEBPACK_IMPORTED_MODULE_1__.Alert);


/***/ }),

/***/ "./frontend/src/controllers/aside_controller.js":
/*!******************************************************!*\
  !*** ./frontend/src/controllers/aside_controller.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    if (window.innerWidth < 1024) {
      this.isOpen = !this.isOpen;
    }
  }
  layout() {
    if (window.innerWidth > 1024) {
      this.overlayTarget.classList.add("opacity-0");
      this.overlayTarget.classList.remove("opacity-100");
    } else {
      this.hide();
    }
  }
  animateHide() {
    setTimeout(() => {
      this.hide();
    }, "300");
  }
  hide() {
    this.containerTarget.classList.add("-translate-x-full");
    this.containerTarget.classList.remove("translate-none");
    this.overlayTarget.classList.add("opacity-0", "hidden");
  }
  show() {
    this.containerTarget.classList.add("translate-none");
    this.containerTarget.classList.remove("-translate-x-full");
    this.overlayTarget.classList.remove("opacity-0", "hidden");
    this.overlayTarget.classList.add("opacity-100");
  }
  close() {
    this.hide();
  }
}
_defineProperty(_Class, "targets", ["container", "hamburger", "close", "overlay"]);
_defineProperty(_Class, "values", {
  isOpen: {
    type: Boolean,
    default: true
  }
});

/***/ }),

/***/ "./frontend/src/controllers/autoheight_controller.js":
/*!***********************************************************!*\
  !*** ./frontend/src/controllers/autoheight_controller.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.setHeight();
  }
  setHeight() {
    let maxHeight = 0;
    this.contentTargets.forEach(element => {
      const elementHeight = element.offsetHeight;
      if (elementHeight > maxHeight) {
        maxHeight = elementHeight;
      }
    });
    this.contentTargets.forEach(element => {
      element.style.height = `${maxHeight}px`;
    });
  }
}
_defineProperty(_Class, "targets", ["content"]);

/***/ }),

/***/ "./frontend/src/controllers/chat/chat_controller.js":
/*!**********************************************************!*\
  !*** ./frontend/src/controllers/chat/chat_controller.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  constructor(...args) {
    super(...args);
    _defineProperty(this, "handleParentFrameLoaded", () => {
      const _this = this;
      if (document.getElementById("chat-messages").src) {
        let currentRoomUrl = document.getElementById("chat-messages").src;
        const currentRoomPath = new URL(currentRoomUrl).pathname;
        const extractedRoomPath = currentRoomPath.replace(/\/$/, '');
        _this.userListItemTargets.forEach(userItem => {
          let userItemHref = userItem.href;
          const userItemPath = new URL(userItemHref).pathname;
          const extractedUserPath = userItemPath.replace(/\/$/, '');
          if (extractedUserPath === extractedRoomPath) {
            setTimeout(() => {
              _this.toggleActiveClass(userItem.closest(".user-item"));
            }, "60");
          }
        });
      }
    });
  }
  connect() {
    document.addEventListener('parent:frame-render', this.handleParentFrameLoaded);
    this.dt;
  }
  showMessages() {
    if (window.innerWidth < 768) {
      setTimeout(() => {
        this.element.classList.add("-translate-x-[100vw]");
      }, "60");
    } else {
      return false;
    }
  }
  toggleChatActions(e) {
    e.stopPropagation();
    const chatActions = this.chatActionsTarget;
    if (chatActions.classList.contains('opacity-0')) {
      chatActions.classList.remove('opacity-0', 'invisible');
      chatActions.classList.add('opacity-100', 'visible');
    } else {
      chatActions.classList.remove('opacity-100', 'visible');
      chatActions.classList.add('opacity-0', 'invisible');
    }
  }
  hideMessages() {
    if (window.innerWidth < 768) {
      this.element.classList.remove("-translate-x-[100vw]");
    } else {
      return false;
    }
  }
  clearStyles() {
    if (window.innerWidth > 768 && this.element.classList.contains("-translate-x-[100vw]")) {
      this.element.classList.remove("-translate-x-[100vw]");
    }
  }
  toggleActiveClass(event) {
    this.userListItemTargets.forEach(item => {
      const listItem = item.closest(".user-item");
      listItem.classList.remove("bg-downy-100");
    });
    event.classList.add("bg-downy-100");
  }
  handleFileChange() {
    this.dt = new DataTransfer();
    this.formWrapTarget.classList.remove("h-[180px]");
    const files = this.inputFileTarget.files;
    const filesList = this.fileListTarget;
    filesList.innerHTML = '';
    if (files.length > 3) {
      this.fileListTarget.innerHTML += `<div class="text-xs font-medium text-red-600" data-chat-target="error">Too many attachments. Max 3 allowed.</div>`;
      this.inputFileTarget.value = '';
    } else {
      for (const file of this.inputFileTarget.files) {
        const fileBlock = document.createElement("span");
        fileBlock.classList.add("list-item-wrap");
        fileBlock.innerHTML = `
        <div class="wrap flex justify-start w-[calc(100%-32px)] gap-2">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="flex-grow-0 flex-shrink-0 w-7 h-7 relative"
                  preserveAspectRatio="xMidYMid meet">
                <rect width="32" height="32" rx="16" fill="#F3FAFA"></rect>
                <path d="M16.6667 9.33325H12C11.6464 9.33325 11.3073 9.47373 11.0572 9.72378C10.8072 9.97382 10.6667 10.313 10.6667 10.6666V21.3333C10.6667 21.6869 10.8072 22.026 11.0572 22.2761C11.3073 22.5261 11.6464 22.6666 12 22.6666H20C20.3536 22.6666 20.6928 22.5261 20.9428 22.2761C21.1929 22.026 21.3334 21.6869 21.3334 21.3333V13.9999M16.6667 9.33325L21.3334 13.9999M16.6667 9.33325V13.9999H21.3334"
                      stroke="#62C6C5" stroke-width="1.33333" stroke-linecap="round"
                      stroke-linejoin="round"></path>
            </svg>
            <div class="overflow-hidden">
                <div class="font-medium whitespace-nowrap truncate file-name">${file.name}</div>
                <div class="text-gray-500">${(file.size / 1048576).toFixed(2) + 'MB'}</div>
            </div>
        </div>`;
        const deleteButton = document.createElement("span");
        deleteButton.classList.add("file-delete");
        deleteButton.innerHTML = `
                                  <svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
                                    <path d="M11 4.5V3.9C11 3.05992 11 2.63988 10.8365 2.31901C10.6927 2.03677 10.4632 1.8073 10.181 1.66349C9.86012 1.5 9.44008 1.5 8.6 1.5H7.4C6.55992 1.5 6.13988 1.5 5.81901 1.66349C5.53677 1.8073 5.3073 2.03677 5.16349 2.31901C5 2.63988 5 3.05992 5 3.9V4.5M6.5 8.625V12.375M9.5 8.625V12.375M1.25 4.5H14.75M13.25 4.5V12.9C13.25 14.1601 13.25 14.7902 13.0048 15.2715C12.789 15.6948 12.4448 16.039 12.0215 16.2548C11.5402 16.5 10.9101 16.5 9.65 16.5H6.35C5.08988 16.5 4.45982 16.5 3.97852 16.2548C3.55516 16.039 3.21095 15.6948 2.99524 15.2715C2.75 14.7902 2.75 14.1601 2.75 12.9V4.5" stroke="#F04438" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                  </svg>`;
        deleteButton.addEventListener("click", event => this.deleteFile(event));
        fileBlock.appendChild(deleteButton);
        filesList.appendChild(fileBlock);
        this.dt.items.add(file);
      }
      this.inputFileTarget.files = this.dt.files;
    }
  }
  deleteFile(event) {
    const name = event.currentTarget.previousElementSibling.querySelector('.file-name').textContent;
    event.currentTarget.parentElement.remove();
    for (let i = 0; i < this.dt.items.length; i++) {
      if (name === this.dt.items[i].getAsFile().name) {
        this.dt.items.remove(i);
        break;
      }
    }
    this.inputFileTarget.files = this.dt.files;
    console.log(this.inputFileTarget.files);
  }
  resetStyle() {
    if (!this.formWrapTarget.classList.contains("h-[180px]")) {
      this.formWrapTarget.classList.add("h-[180px]");
    }
  }
  validateTextarea(event) {
    if (this.textareaTarget.value.trim() === '') {
      event.preventDefault();
      this.formWrapTarget.classList.remove("h-[180px]");
      let parentElement = this.fileListTarget.parentNode;
      let errorMessageDiv = document.createElement('div');
      errorMessageDiv.className = 'w-full text-xs font-medium text-red-600 order-1';
      errorMessageDiv.textContent = 'Message cannot be empty';
      parentElement.prepend(errorMessageDiv);
    } else {
      this.resetStyle();
      setTimeout(() => {
        this.dt.clearData();
      }, "50");
    }
  }
}
_defineProperty(_Class, "targets", ["userListItem", "inputFile", "fileList", "fileName", "formWrap", "textarea", "emptyView", "dialog", "error", "chatActions"]);

/***/ }),

/***/ "./frontend/src/controllers/chat/chat_message_controller.js":
/*!******************************************************************!*\
  !*** ./frontend/src/controllers/chat/chat_message_controller.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  constructor(...args) {
    super(...args);
    _defineProperty(this, "handleParentFrameLoaded", () => {
      const _this = this;
      _this.addMessageClass();
    });
  }
  connect() {
    this.addMessageClass();
    document.addEventListener('parent:frame-render', this.handleParentFrameLoaded);
  }
  addMessageClass() {
    let currentUser = document.getElementById("chat").getAttribute("data-current-user-value");
    let sender = this.senderValue;
    if (sender == currentUser) {
      this.element.classList.add("message-own");
    } else if (sender == '') {
      this.element.classList.add("message-system");
    } else {
      this.element.classList.add("message-recieved");
    }
  }
}
_defineProperty(_Class, "values", {
  sender: {
    type: String,
    default: ''
  }
});

/***/ }),

/***/ "./frontend/src/controllers/collapse_controller.js":
/*!*********************************************************!*\
  !*** ./frontend/src/controllers/collapse_controller.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.setDefaultState();
  }
  setDefaultState() {
    if (this.openOnLoadValue) {
      this.show;
    } else {
      this.hide();
    }
  }
  toggle() {
    if (this.isOpenValue) {
      this.hide();
      this.isOpenValue = !this.isOpenValue;
    } else {
      this.show();
      this.isOpenValue = !this.isOpenValue;
    }
  }
  hide() {
    this.element.style.height = this.triggerElTarget.offsetHeight + "px";
    this.arrowDownTarget.classList.add("hidden");
    this.arrowUpTarget.classList.remove("hidden");
  }
  show() {
    this.element.style.height = this.targetElTarget.offsetHeight + this.triggerElTarget.offsetHeight + "px";
    this.arrowDownTarget.classList.remove("hidden");
    this.arrowUpTarget.classList.add("hidden");
  }
}
_defineProperty(_Class, "targets", ["triggerEl", "targetEl", "arrowDown", "arrowUp"]);
_defineProperty(_Class, "values", {
  isOpen: {
    type: Boolean,
    default: false
  },
  openOnLoad: {
    type: Boolean,
    default: false
  }
});

/***/ }),

/***/ "./frontend/src/controllers/cookie_banner_controller.js":
/*!**************************************************************!*\
  !*** ./frontend/src/controllers/cookie_banner_controller.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.showBanner();
    //localStorage.clear();
  }
  showBanner() {
    const isBannerClicked = localStorage.getItem("CookieBannerClicked");
    if (isBannerClicked === null) {
      this.element.classList.remove("hidden");
    }
  }
  hideBanner() {
    this.element.classList.add("hidden");
  }
  setLocalStorageItem() {
    localStorage.setItem("CookieBannerClicked", true);
    this.hideBanner();
  }
  disconnect() {
    this.element.remove();
  }
}
_defineProperty(_Class, "targets", ["banner"]);

/***/ }),

/***/ "./frontend/src/controllers/disable_button_controller.js":
/*!***************************************************************!*\
  !*** ./frontend/src/controllers/disable_button_controller.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.buttonClicked = false;
  }
  Disable(event) {
    if (!this.buttonClicked) {
      this.buttonClicked = true;
    } else {
      event.preventDefault();
    }
  }
});

/***/ }),

/***/ "./frontend/src/controllers/dropdown_controller.js":
/*!*********************************************************!*\
  !*** ./frontend/src/controllers/dropdown_controller.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
/* harmony import */ var flowbite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! flowbite */ "./node_modules/flowbite/lib/esm/index.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }


class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    new flowbite__WEBPACK_IMPORTED_MODULE_1__.Dropdown(this.dropmenuTarget, this.triggerTarget, {});
  }
}
_defineProperty(_Class, "targets", ["trigger", "dropmenu"]);

/***/ }),

/***/ "./frontend/src/controllers/duplicate_form_controller.js":
/*!***************************************************************!*\
  !*** ./frontend/src/controllers/duplicate_form_controller.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.checkDeleted();
  }
  duplicate() {
    let totalFormsElement = document.querySelector('[id$="TOTAL_FORMS"]');
    let formIndex = totalFormsElement.getAttribute('value');
    formIndex = parseInt(formIndex);
    let duplicateForm = this.emptyFormTarget.cloneNode(true);
    duplicateForm.classList.remove("hidden");
    let newContent = duplicateForm.innerHTML.replace(/__prefix__/g, formIndex);
    let newForm = document.createElement('div');
    newForm.classList.add("new-form");
    newForm.innerHTML = newContent;
    this.formsetTarget.append(newForm);
    totalFormsElement.setAttribute("value", formIndex + 1);
  }
  hideForm(event) {
    const form = event.target.closest('.new-form');
    const checkbox = event.target.closest('.new-form').querySelector('.checkboxinput');
    checkbox.checked = true;
    form.classList.add("hidden");
  }
  checkDeleted() {
    let checkboxes = document.querySelectorAll(".checkboxinput");
    checkboxes.forEach(checkbox => {
      if (checkbox.checked) {
        let closestNewForm = checkbox.closest(".new-form");
        let closestFormsetDivider = checkbox.closest(".formset-divider");
        if (closestNewForm) {
          closestNewForm.classList.add("hidden");
        }
        if (closestFormsetDivider) {
          closestFormsetDivider.classList.add("hidden");
        }
      }
    });
  }
}
_defineProperty(_Class, "targets", ["emptyForm", "formset"]);

/***/ }),

/***/ "./frontend/src/controllers/file_upload_controller.js":
/*!************************************************************!*\
  !*** ./frontend/src/controllers/file_upload_controller.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {}
  readURL() {
    this.fileListTarget.innerHTML = '';
    const files = this.inputTarget.files;
    for (let i = 0; i < files.length; i++) {
      this.fileListTarget.innerHTML += `    <div class="w-full border border-downy-300 bg-white rounded-lg px-4 py-3 mb-4">
                                              <div class="flex justify-start gap-4 flex-wrap">
                                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none"
                                                      xmlns="http://www.w3.org/2000/svg"
                                                      class="flex-grow-0 flex-shrink-0 w-7 h-7 relative"
                                                      preserveAspectRatio="xMidYMid meet">
                                                    <rect width="32" height="32" rx="16" fill="#F3FAFA"></rect>
                                                    <path d="M16.6667 9.33325H12C11.6464 9.33325 11.3073 9.47373 11.0572 9.72378C10.8072 9.97382 10.6667 10.313 10.6667 10.6666V21.3333C10.6667 21.6869 10.8072 22.026 11.0572 22.2761C11.3073 22.5261 11.6464 22.6666 12 22.6666H20C20.3536 22.6666 20.6928 22.5261 20.9428 22.2761C21.1929 22.026 21.3334 21.6869 21.3334 21.3333V13.9999M16.6667 9.33325L21.3334 13.9999M16.6667 9.33325V13.9999H21.3334"
                                                          stroke="#62C6C5" stroke-width="1.33333" stroke-linecap="round"
                                                          stroke-linejoin="round"></path>
                                                </svg>
                                                <div class="w-[calc(100%-80px)]  max-w-[calc(100%-80px)] overflow-hidden">
                                                    <div class="font-medium truncate">${files[i].name}</div>
                                                    <div class="text-gray-500">${(files[i].size / 1048576).toFixed(2) + 'MB'}</div>
                                                </div>
                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                                  xmlns="http://www.w3.org/2000/svg" class="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                                                  preserveAspectRatio="none">
                                                  <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" fill="#62C6C5"></rect>
                                                  <path d="M11.3333 5.5L6.75001 10.0833L4.66667 8" stroke="white" stroke-width="1.66667"
                                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                                  <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" stroke="#62C6C5"></rect>
                                                </svg>
                                              </div>
                                            </div>`;
    }
  }
}
_defineProperty(_Class, "targets", ['input', 'fileList']);

/***/ }),

/***/ "./frontend/src/controllers/file_upload_validation_controller.js":
/*!***********************************************************************!*\
  !*** ./frontend/src/controllers/file_upload_validation_controller.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  initialize() {
    if (this.setAttributesValue) {
      this.setAttributes();
    }
  }
  connect() {
    this.buttonClicked = false;
    console.log(this.setAttributesValue);
  }
  setAttributes() {
    this.element.querySelector('input[type="file"]').setAttribute("data-file-upload-validation-target", "inputFile");
  }
  validateFileLength(event) {
    const files = this.inputFileTarget.files;
    if (files.length > this.maxFileLengthValue) {
      event.preventDefault();
      this.buttonClicked = false;
      this.maxFileLengthTarget.innerHTML = this.maxFileLengthValue;
      this.errorTarget.classList.remove("hidden");
    } else {
      this.DisableFormSubmit(event);
    }
  }
  DisableFormSubmit(event) {
    if (!this.buttonClicked) {
      this.buttonClicked = true;
    } else {
      event.preventDefault();
    }
  }
  disconnect() {
    this.element.remove();
  }
}
_defineProperty(_Class, "targets", ["inputFile", "error", "maxFileLength"]);
_defineProperty(_Class, "values", {
  maxFileLength: {
    type: Number,
    default: 10
  },
  setAttributes: {
    type: Boolean,
    default: false
  }
});

/***/ }),

/***/ "./frontend/src/controllers/filter_deals_controller.js":
/*!*************************************************************!*\
  !*** ./frontend/src/controllers/filter_deals_controller.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {}
  showAll(e) {
    this.removeActiveClass();
    this.filteredElementTargets.forEach(element => {
      element.classList.remove('hidden');
      element.classList.add('block');
    });
    e.target.classList.add("border-downy-300", "text-downy-300");
    e.target.classList.remove("border-transparent");
    this.hideEmptySearchResult();
  }
  showItemsByCategory(category, e) {
    this.removeActiveClass();
    this.filteredElementTargets.forEach(element => {
      if (!element.dataset[category]) {
        element.classList.add('hidden');
      } else {
        element.classList.remove('hidden');
      }
    });
    e.target.classList.add("border-downy-300", "text-downy-300");
    e.target.classList.remove("border-transparent");
    const allHidden = this.filteredElementTargets.every(element => element.classList.contains('hidden'));
    if (allHidden) {
      this.showEmptySearchResult();
    } else {
      this.hideEmptySearchResult();
    }
  }
  showDialogs(e) {
    this.removeActiveClass();
    this.showItemsByCategory('dialog', e);
  }
  showProposals(e) {
    this.removeActiveClass();
    this.showItemsByCategory('proposal', e);
  }
  showProgresses(e) {
    this.removeActiveClass();
    this.showItemsByCategory('progress', e);
  }
  showCompletes(e) {
    this.removeActiveClass();
    this.showItemsByCategory('complete', e);
  }
  removeActiveClass() {
    this.buttonTargets.forEach(button => {
      button.classList.remove("border-downy-300", "text-downy-300");
      button.classList.add("border-transparent");
    });
  }
  showEmptySearchResult() {
    this.emptySearchTarget.classList.remove("hidden");
  }
  hideEmptySearchResult() {
    this.emptySearchTarget.classList.add("hidden");
  }
}
_defineProperty(_Class, "targets", ["filteredElement", "button", "emptySearch"]);

/***/ }),

/***/ "./frontend/src/controllers/filter_form_controller.js":
/*!************************************************************!*\
  !*** ./frontend/src/controllers/filter_form_controller.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  initialize() {
    this.setAttributes();
  }
  connect() {}
  setAttributes() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
      checkbox.setAttribute("data-filter-form-target", "checkbox");
    });
    if (this.typeValue == "crelancers") {
      const priceMin = document.querySelector('input[name="rate_min"]');
      priceMin.setAttribute("data-filter-form-target", "priceMin");
      const priceMax = document.querySelector('input[name="rate_max"]');
      priceMax.setAttribute("data-filter-form-target", "priceMax");
    }
    if (this.typeValue == "jobs") {
      const priceMin = document.querySelector('input[name="budget_min"]');
      priceMin.setAttribute("data-filter-form-target", "priceMin");
      const priceMax = document.querySelector('input[name="budget_max"]');
      priceMax.setAttribute("data-filter-form-target", "priceMax");
    }
    const state = document.getElementById('id_state');
    state.setAttribute("data-filter-form-target", "state");
    const skills = document.getElementById("id_skills_tags_input");
    skills.setAttribute("data-filter-form-target", "skills");
  }
  submitClick() {
    this.submitTarget.click();
  }
  updateForm() {
    this.updateSearchInput();
    this.submitClick();
  }
  clearForm() {
    this.clearSearchInput();
    this.submitClick();
  }
  updateSearchInput() {
    this.searchOutTarget.value = this.searchInTarget.value;
  }
  clearSearchInput() {
    this.searchInTarget.value = '';
    this.searchOutTarget.value = this.searchInTarget.value;
  }
  clearAll() {
    this.checkboxTargets.forEach(checkbox => {
      checkbox.checked = false;
    });
    this.priceMinTarget.value = '';
    this.priceMaxTarget.value = '';
    this.stateTarget.value = '';
    this.skillsTarget.value = '';
    this.searchInTarget.value = '';
    this.searchOutTarget.value = '';
    document.querySelectorAll(".tag").forEach(tag => {
      tag.remove();
    });
    this.submitClick();
  }
  setUrl() {
    const frame = this.searchResultTarget;
    frame.addEventListener("turbo:frame-load", this.onFrameLoad.bind(this));
  }
  onFrameLoad(event) {
    const url = event.target.getAttribute("src");
    window.history.replaceState({}, "", url);
  }
}
_defineProperty(_Class, "targets", ["submit", "clear", "searchIn", "searchOut", "checkbox", "priceMin", "priceMax", "state", "skills", "skillTag", "searchResult"]);
_defineProperty(_Class, "values", {
  type: {
    type: String,
    default: ""
  }
});

/***/ }),

/***/ "./frontend/src/controllers/filter_jobs_controller.js":
/*!************************************************************!*\
  !*** ./frontend/src/controllers/filter_jobs_controller.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {}
  showAll(e) {
    this.removeActiveClass();
    this.filteredElementTargets.forEach(element => {
      element.classList.remove('hidden');
      element.classList.add('block');
    });
    e.target.classList.add("border-downy-300", "text-downy-300");
    e.target.classList.remove("border-transparent");
    this.hideEmptySearchResult();
  }
  showItemsByCategory(category, e) {
    this.removeActiveClass();
    this.filteredElementTargets.forEach(element => {
      if (!element.dataset[category]) {
        element.classList.add('hidden');
      } else {
        element.classList.remove('hidden');
      }
    });
    e.target.classList.add("border-downy-300", "text-downy-300");
    e.target.classList.remove("border-transparent");
    const allHidden = this.filteredElementTargets.every(element => element.classList.contains('hidden'));
    if (allHidden) {
      this.showEmptySearchResult();
    } else {
      this.hideEmptySearchResult();
    }
  }
  showDrafts(e) {
    this.removeActiveClass();
    this.showItemsByCategory('drafts', e);
  }
  showPublics(e) {
    this.removeActiveClass();
    this.showItemsByCategory('public', e);
  }
  showProgresses(e) {
    this.removeActiveClass();
    this.showItemsByCategory('progress', e);
  }
  showCanceled(e) {
    this.removeActiveClass();
    this.showItemsByCategory('canceled', e);
  }
  removeActiveClass() {
    this.buttonTargets.forEach(button => {
      button.classList.remove("border-downy-300", "text-downy-300");
      button.classList.add("border-transparent");
    });
  }
  showEmptySearchResult() {
    this.emptySearchTarget.classList.remove("hidden");
  }
  hideEmptySearchResult() {
    this.emptySearchTarget.classList.add("hidden");
  }
}
_defineProperty(_Class, "targets", ["filteredElement", "button", "emptySearch"]);

/***/ }),

/***/ "./frontend/src/controllers/gallery_controller.js":
/*!********************************************************!*\
  !*** ./frontend/src/controllers/gallery_controller.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
/* harmony import */ var photoswipe_lightbox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! photoswipe/lightbox */ "./node_modules/photoswipe/dist/photoswipe-lightbox.esm.js");
/* harmony import */ var photoswipe_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! photoswipe/style.css */ "./node_modules/photoswipe/dist/photoswipe.css");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }



class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.setImageSize();
    this.initGallery();
  }
  initGallery() {
    this.lightbox = new photoswipe_lightbox__WEBPACK_IMPORTED_MODULE_1__["default"]({
      gallery: this.element,
      children: "a",
      pswpModule: () => __webpack_require__.e(/*! import() */ "vendors-node_modules_photoswipe_dist_photoswipe_esm_js").then(__webpack_require__.bind(__webpack_require__, /*! photoswipe */ "./node_modules/photoswipe/dist/photoswipe.esm.js"))
    });
    this.lightbox.init();
  }
  setImageSize() {
    this.imageTargets.forEach(image => {
      const fullsizeSrc = image.getAttribute('data-fullsize-src');
      const img = new Image();
      img.src = fullsizeSrc;
      img.onload = function () {
        const width = img.naturalWidth;
        const height = img.naturalHeight;
        image.parentNode.setAttribute("data-pswp-width", width);
        image.parentNode.setAttribute("data-pswp-height", height);
      };
    });
  }
}
_defineProperty(_Class, "targets", ["image"]);

/***/ }),

/***/ "./frontend/src/controllers/index.js":
/*!*******************************************!*\
  !*** ./frontend/src/controllers/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _application__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./application */ "./frontend/src/controllers/application.js");
/* harmony import */ var _anchor_movement_controller__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./anchor_movement_controller */ "./frontend/src/controllers/anchor_movement_controller.js");
/* harmony import */ var _aside_controller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./aside_controller */ "./frontend/src/controllers/aside_controller.js");
/* harmony import */ var _file_upload_controller__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./file_upload_controller */ "./frontend/src/controllers/file_upload_controller.js");
/* harmony import */ var _autoheight_controller__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./autoheight_controller */ "./frontend/src/controllers/autoheight_controller.js");
/* harmony import */ var _slider_controller__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./slider_controller */ "./frontend/src/controllers/slider_controller.js");
/* harmony import */ var _dropdown_controller__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./dropdown_controller */ "./frontend/src/controllers/dropdown_controller.js");
/* harmony import */ var _padding_value_controller__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./padding_value_controller */ "./frontend/src/controllers/padding_value_controller.js");
/* harmony import */ var _duplicate_form_controller__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./duplicate_form_controller */ "./frontend/src/controllers/duplicate_form_controller.js");
/* harmony import */ var _filter_form_controller__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./filter_form_controller */ "./frontend/src/controllers/filter_form_controller.js");
/* harmony import */ var _filter_deals_controller__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./filter_deals_controller */ "./frontend/src/controllers/filter_deals_controller.js");
/* harmony import */ var _filter_jobs_controller__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./filter_jobs_controller */ "./frontend/src/controllers/filter_jobs_controller.js");
/* harmony import */ var _responsive_filter_controller__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./responsive_filter_controller */ "./frontend/src/controllers/responsive_filter_controller.js");
/* harmony import */ var _collapse_controller__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./collapse_controller */ "./frontend/src/controllers/collapse_controller.js");
/* harmony import */ var _gallery_controller__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./gallery_controller */ "./frontend/src/controllers/gallery_controller.js");
/* harmony import */ var _read_more_controller__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./read_more_controller */ "./frontend/src/controllers/read_more_controller.js");
/* harmony import */ var _navbar_controller__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./navbar_controller */ "./frontend/src/controllers/navbar_controller.js");
/* harmony import */ var _popup_controller__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./popup_controller */ "./frontend/src/controllers/popup_controller.js");
/* harmony import */ var _chat_chat_controller__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./chat/chat_controller */ "./frontend/src/controllers/chat/chat_controller.js");
/* harmony import */ var _scroll_to_down_controller__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./scroll_to_down_controller */ "./frontend/src/controllers/scroll_to_down_controller.js");
/* harmony import */ var _chat_chat_message_controller__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./chat/chat_message_controller */ "./frontend/src/controllers/chat/chat_message_controller.js");
/* harmony import */ var _stripe_controller__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./stripe_controller */ "./frontend/src/controllers/stripe_controller.js");
/* harmony import */ var _disable_button_controller__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./disable_button_controller */ "./frontend/src/controllers/disable_button_controller.js");
/* harmony import */ var _file_upload_validation_controller__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./file_upload_validation_controller */ "./frontend/src/controllers/file_upload_validation_controller.js");
/* harmony import */ var _cookie_banner_controller__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./cookie_banner_controller */ "./frontend/src/controllers/cookie_banner_controller.js");
/* harmony import */ var _review_modal_controller__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./review_modal_controller */ "./frontend/src/controllers/review_modal_controller.js");


_application__WEBPACK_IMPORTED_MODULE_0__.application.register("anchor-movement", _anchor_movement_controller__WEBPACK_IMPORTED_MODULE_1__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("aside", _aside_controller__WEBPACK_IMPORTED_MODULE_2__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("file-upload", _file_upload_controller__WEBPACK_IMPORTED_MODULE_3__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("autoheight", _autoheight_controller__WEBPACK_IMPORTED_MODULE_4__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("slider", _slider_controller__WEBPACK_IMPORTED_MODULE_5__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("dropdown", _dropdown_controller__WEBPACK_IMPORTED_MODULE_6__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("padding-value", _padding_value_controller__WEBPACK_IMPORTED_MODULE_7__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("duplicate-form", _duplicate_form_controller__WEBPACK_IMPORTED_MODULE_8__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("filter-form", _filter_form_controller__WEBPACK_IMPORTED_MODULE_9__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("filter-deals", _filter_deals_controller__WEBPACK_IMPORTED_MODULE_10__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("filter-jobs", _filter_jobs_controller__WEBPACK_IMPORTED_MODULE_11__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("responsive-filter", _responsive_filter_controller__WEBPACK_IMPORTED_MODULE_12__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("collapse", _collapse_controller__WEBPACK_IMPORTED_MODULE_13__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("gallery", _gallery_controller__WEBPACK_IMPORTED_MODULE_14__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("read-more", _read_more_controller__WEBPACK_IMPORTED_MODULE_15__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("navbar", _navbar_controller__WEBPACK_IMPORTED_MODULE_16__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("popup", _popup_controller__WEBPACK_IMPORTED_MODULE_17__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("chat", _chat_chat_controller__WEBPACK_IMPORTED_MODULE_18__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("scroll-to-down", _scroll_to_down_controller__WEBPACK_IMPORTED_MODULE_19__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("chat-message", _chat_chat_message_controller__WEBPACK_IMPORTED_MODULE_20__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("stripe", _stripe_controller__WEBPACK_IMPORTED_MODULE_21__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("disable-button", _disable_button_controller__WEBPACK_IMPORTED_MODULE_22__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("file-upload-validation", _file_upload_validation_controller__WEBPACK_IMPORTED_MODULE_23__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("cookie-banner", _cookie_banner_controller__WEBPACK_IMPORTED_MODULE_24__["default"]);

_application__WEBPACK_IMPORTED_MODULE_0__.application.register("review-modal", _review_modal_controller__WEBPACK_IMPORTED_MODULE_25__["default"]);

/***/ }),

/***/ "./frontend/src/controllers/navbar_controller.js":
/*!*******************************************************!*\
  !*** ./frontend/src/controllers/navbar_controller.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
/* harmony import */ var flowbite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! flowbite */ "./node_modules/flowbite/lib/esm/index.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }


class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    new flowbite__WEBPACK_IMPORTED_MODULE_1__.Collapse(this.menuTarget, this.triggerTarget);
  }
}
_defineProperty(_Class, "targets", ["menu", "trigger"]);

/***/ }),

/***/ "./frontend/src/controllers/padding_value_controller.js":
/*!**************************************************************!*\
  !*** ./frontend/src/controllers/padding_value_controller.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.setTopValue();
  }
  setTopValue() {
    this.element.style.paddingTop = Math.floor(document.getElementById("header").offsetHeight) - 1 + "px";
  }
});

/***/ }),

/***/ "./frontend/src/controllers/popup_controller.js":
/*!******************************************************!*\
  !*** ./frontend/src/controllers/popup_controller.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    console.log("popup connected");
    this.show();
  }
  show() {
    setTimeout(() => {
      this.element.classList.remove("hidden");
      this.element.classList.add("block");
    }, "5000");
  }
  hide() {
    this.element.classList.remove("block");
    this.element.classList.add("hidden");
    this.element.remove();
  }
  disconnect() {
    this.element.remove();
  }
});

/***/ }),

/***/ "./frontend/src/controllers/read_more_controller.js":
/*!**********************************************************!*\
  !*** ./frontend/src/controllers/read_more_controller.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var stimulus_read_more__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stimulus-read-more */ "./node_modules/stimulus-read-more/dist/stimulus-read-more.mjs");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends stimulus_read_more__WEBPACK_IMPORTED_MODULE_0__["default"] {
  connect() {
    super.connect();
    this.hideButton();
  }
  hideButton() {
    if (this.contentTarget.innerHTML.length < this.maxLengthValue) {
      this.buttonTarget.classList.add("hidden");
    }
  }
}
_defineProperty(_Class, "targets", ["button"]);
_defineProperty(_Class, "values", {
  maxLength: {
    type: Number,
    default: 150
  }
});

/***/ }),

/***/ "./frontend/src/controllers/responsive_filter_controller.js":
/*!******************************************************************!*\
  !*** ./frontend/src/controllers/responsive_filter_controller.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.moveFilter();
  }
  moveFilter() {
    if (window.innerWidth < 1024) {
      this.mobileWrapTarget.append(this.filterTarget);
      this.mobileWrapTarget.classList.remove("hidden");
    } else {
      this.desktopWrapTarget.append(this.filterTarget);
      this.mobileWrapTarget.classList.add("hidden");
      this.mobileWrapTarget.classList.add("translate-y-[200%]");
      document.body.classList.remove("overflow-hidden");
    }
  }
  showFilter() {
    this.mobileWrapTarget.classList.remove("translate-y-[200%]");
    document.body.classList.add("overflow-hidden");
  }
  hideFilter() {
    this.mobileWrapTarget.classList.add("translate-y-[200%]");
    document.body.classList.remove("overflow-hidden");
  }
}
_defineProperty(_Class, "targets", ["mobileWrap", "desktopWrap", "filter"]);

/***/ }),

/***/ "./frontend/src/controllers/review_modal_controller.js":
/*!*************************************************************!*\
  !*** ./frontend/src/controllers/review_modal_controller.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
/* harmony import */ var flowbite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! flowbite */ "./node_modules/flowbite/lib/esm/index.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }



// Connects to data-controller="modal"
class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.initModal();
  }
  initModal() {
    this.modal = new flowbite__WEBPACK_IMPORTED_MODULE_1__.Modal(this.modalTarget, {
      backdropClasses: 'bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-[101]',
      onShow: () => {
        this.setFormAction();
      }
    });
  }
  hide() {
    this.modal.hide();
  }
  show() {
    this.modal.show();
  }
  setFormAction() {
    console.log(this.formActionValue);
    this.formTarget.setAttribute("action", this.formActionValue);
  }
  disconnect() {
    this.modal.hide();
    this.modal.destroy();
  }
}
_defineProperty(_Class, "targets", ["modal", "form"]);
_defineProperty(_Class, "values", {
  formAction: String
});

/***/ }),

/***/ "./frontend/src/controllers/scroll_to_down_controller.js":
/*!***************************************************************!*\
  !*** ./frontend/src/controllers/scroll_to_down_controller.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  constructor(...args) {
    super(...args);
    _defineProperty(this, "handleParentFrameLoaded", () => {
      const _this = this;
      _this.scrollToDown();
    });
  }
  connect() {
    document.addEventListener('parent:frame-render', this.handleParentFrameLoaded);
    this.scrollToDown();
  }
  scrollToDown() {
    this.listTarget.scrollTop = this.listTarget.scrollHeight;
  }
}
_defineProperty(_Class, "targets", ["list", "userListItem"]);

/***/ }),

/***/ "./frontend/src/controllers/slider_controller.js":
/*!*******************************************************!*\
  !*** ./frontend/src/controllers/slider_controller.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var stimulus_carousel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stimulus-carousel */ "./node_modules/stimulus-carousel/dist/stimulus-carousel.mjs");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (class extends stimulus_carousel__WEBPACK_IMPORTED_MODULE_0__["default"] {
  connect() {
    super.connect();

    // The swiper instance.
    this.swiper;

    // Default options for every carousels.
    this.defaultOptions;
  }
});

/***/ }),

/***/ "./frontend/src/controllers/stripe_controller.js":
/*!*******************************************************!*\
  !*** ./frontend/src/controllers/stripe_controller.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _Class)
/* harmony export */ });
/* harmony import */ var _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @hotwired/stimulus */ "./node_modules/@hotwired/stimulus/dist/stimulus.js");
/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @stripe/stripe-js */ "./node_modules/@stripe/stripe-js/dist/stripe.esm.js");
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }


class _Class extends _hotwired_stimulus__WEBPACK_IMPORTED_MODULE_0__.Controller {
  connect() {
    this.initializeStripe();
  }
  async initializeStripe() {
    // const stripe = await loadStripe(this.apiKeyValue);
    // this.stripe = stripe;
    // let elements = stripe.elements();
    // let cardElement = elements.create('card');
    //
    // // Add an instance of the card Element into the `card-element` div.
    // cardElement.mount(this.cardElementTarget);
    //
    // // Handle form creating payment method and submitting to server
    // let form = this.paymentFormTarget;
    // let paymentTokenField = this.paymentTokenTarget;
    // let errorElement = this.cardErrorsTarget;
    // let submitButton = this.paymentProcessTarget;
    //
    // submitButton.addEventListener("click", function () {
    //     stripe.createToken(cardElement).then(function (result) {
    //         if (result.error) {
    //             // Show Stripe error in payment form
    //             errorElement.textContent = result.error.message;
    //         } else {
    //             // Token represents the card and can be sent to your server.
    //             paymentTokenField.value = result.token.id;
    //             form.submit();
    //         }
    //     });
    // });

    // ======================

    // console.log('this.apiKeyValue', this.apiKeyValue);
    // console.log('this.clientSecretValue', this.clientSecretValue);
    // console.log('this.returnUrlValue', this.returnUrlValue);

    const stripe = await (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_1__.loadStripe)(this.apiKeyValue);
    const options = {
      // clientSecret: '{{client_secret}}',
      clientSecret: this.clientSecretValue,
      currency: 'usd',
      appearance: {/*...*/}
    };

    // Set up Stripe.js and Elements using the SetupIntent's client secret
    const elements = stripe.elements(options);

    // Create and mount the Payment Element
    const paymentElement = elements.create('payment');
    paymentElement.mount('#payment-element');

    //  ----- checkout.js -----
    // const return_url = '{{ request.scheme }}://{{ request.get_host }}{% url "finance:client_pm_status" %}';
    const return_url = this.returnUrlValue;
    const form = document.getElementById('payment-form');
    form.addEventListener('submit', async event => {
      event.preventDefault();
      const {
        error
      } = await stripe.confirmSetup({
        //`Elements` instance that was used to create the Payment Element
        elements,
        confirmParams: {
          return_url: return_url
        }
      });
      if (error) {
        // This point will only be reached if there is an immediate error when
        // confirming the payment. Show error to your customer (for example, payment
        // details incomplete)
        const messageContainer = document.querySelector('#error-message');
        messageContainer.textContent = error.message;
      } else {
        // Your customer will be redirected to your `return_url`. For some payment
        // methods like iDEAL, your customer will be redirected to an intermediate
        // site first to authorize the payment, then redirected to the `return_url`.
      }
    });
  }
  disconnect() {
    this.element.remove();
  }
}
_defineProperty(_Class, "targets", ["cardElement", "paymentForm", "paymentToken", "cardErrors", "paymentProcess"]);
_defineProperty(_Class, "values", {
  apiKey: String,
  clientSecret: String,
  returnUrl: String
});

/***/ }),

/***/ "./frontend/src/styles/crelancer.scss":
/*!********************************************!*\
  !*** ./frontend/src/styles/crelancer.scss ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// extracted by mini-css-extract-plugin

    if(true) {
      (function() {
        var localsJsonString = undefined;
        // 1755610266486
        var cssReload = __webpack_require__(/*! ../../../node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js */ "./node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js")(module.id, {});
        // only invalidate when locals change
        if (
          module.hot.data &&
          module.hot.data.value &&
          module.hot.data.value !== localsJsonString
        ) {
          module.hot.invalidate();
        } else {
          module.hot.accept();
        }
        module.hot.dispose(function(data) {
          data.value = localsJsonString;
          cssReload();
        });
      })();
    }
  

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_hotwired_turbo_dist_turbo_es2017-esm_js-node_modules_stripe_stripe-js_di-104ff1"], () => (__webpack_exec__("./node_modules/webpack-dev-server/client/index.js?protocol=ws%3A&hostname=0.0.0.0&port=9091&pathname=%2Fws&logging=info&overlay=true&reconnect=10&hot=true&live-reload=true"), __webpack_exec__("./node_modules/webpack/hot/dev-server.js"), __webpack_exec__("./frontend/src/application/crelancer.js")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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