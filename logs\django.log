2025-08-19 12:38:40: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 12:39:51: INFO /app/backend/settings/base.py changed, reloading. [django.utils.autoreload:251]
2025-08-19 12:39:59: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 12:41:11: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 12:45:08: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 12:47:26: INFO /app/backend/settings/base.py changed, reloading. [django.utils.autoreload:251]
2025-08-19 12:47:31: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 12:47:56: INFO /app/backend/settings/base.py changed, reloading. [django.utils.autoreload:251]
2025-08-19 12:48:01: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 12:56:47: INFO /app/backend/settings/production_local.py changed, reloading. [django.utils.autoreload:251]
2025-08-19 12:56:51: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 13:02:28: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 13:03:56: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 13:04:00: INFO HTTP/2 support not enabled (install the http2 and tls Twisted extras) [daphne.server:119]
2025-08-19 13:04:00: INFO Configuring endpoint tcp:port=8000:interface=0.0.0.0 [daphne.server:128]
2025-08-19 13:04:00: INFO Listening on TCP address 0.0.0.0:8000 [daphne.server:159]
2025-08-19 13:05:02: INFO HTTP GET / 200 [0.68, **********:45390] [django.channels.server:168]
2025-08-19 13:05:05: INFO HTTP POST /tz_detect/set/ 200 [0.24, **********:45390] [django.channels.server:168]
2025-08-19 13:07:40: INFO HTTP GET / 200 [0.96, **********:58942] [django.channels.server:168]
2025-08-19 13:17:06: INFO HTTP GET / 200 [0.36, **********:51564] [django.channels.server:168]
2025-08-19 13:17:09: INFO HTTP GET / 200 [0.19, **********:51564] [django.channels.server:168]
2025-08-19 13:17:19: INFO HTTP GET / 200 [0.57, **********:51564] [django.channels.server:168]
2025-08-19 13:17:22: WARNING Not Found: /.well-known/appspecific/com.chrome.devtools.json [django.request:224]
2025-08-19 13:17:22: WARNING HTTP GET /.well-known/appspecific/com.chrome.devtools.json 404 [1.18, **********:51564] [django.channels.server:176]
2025-08-19 13:17:31: INFO HTTP GET / 200 [0.19, **********:51564] [django.channels.server:168]
2025-08-19 13:17:32: WARNING Not Found: /.well-known/appspecific/com.chrome.devtools.json [django.request:224]
2025-08-19 13:17:32: WARNING HTTP GET /.well-known/appspecific/com.chrome.devtools.json 404 [0.63, **********:56292] [django.channels.server:176]
2025-08-19 13:22:14: INFO HTTP GET / 200 [0.26, **********:38878] [django.channels.server:168]
2025-08-19 13:24:33: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 13:24:38: INFO HTTP/2 support not enabled (install the http2 and tls Twisted extras) [daphne.server:119]
2025-08-19 13:24:38: INFO Configuring endpoint tcp:port=8000:interface=0.0.0.0 [daphne.server:128]
2025-08-19 13:24:38: INFO Listening on TCP address 0.0.0.0:8000 [daphne.server:159]
2025-08-19 13:24:45: INFO HTTP GET / 200 [0.57, **********:46758] [django.channels.server:168]
2025-08-19 13:24:57: WARNING Not Found: /.well-known/appspecific/com.chrome.devtools.json [django.request:224]
2025-08-19 13:24:57: WARNING HTTP GET /.well-known/appspecific/com.chrome.devtools.json 404 [0.74, **********:46758] [django.channels.server:176]
2025-08-19 13:25:00: INFO HTTP GET /favicon.ico 302 [0.18, **********:46758] [django.channels.server:174]
2025-08-19 13:26:30: INFO Watching for file changes with StatReloader [django.utils.autoreload:637]
2025-08-19 13:26:33: INFO HTTP/2 support not enabled (install the http2 and tls Twisted extras) [daphne.server:119]
2025-08-19 13:26:33: INFO Configuring endpoint tcp:port=8000:interface=0.0.0.0 [daphne.server:128]
2025-08-19 13:26:33: INFO Listening on TCP address 0.0.0.0:8000 [daphne.server:159]
2025-08-19 13:26:44: INFO HTTP GET / 200 [2.17, **********:52068] [django.channels.server:168]
2025-08-19 13:26:44: INFO HTTP GET /static/jquery.tagsinput-revisited-2.0.min.css 301 [0.00, **********:47002] [django.channels.server:174]
2025-08-19 13:26:44: INFO HTTP GET /static/jquery-ui-1.12.1.min.css 301 [0.00, **********:47018] [django.channels.server:174]
2025-08-19 13:26:44: INFO HTTP GET /static/vendors/images/landing/slider_single/chelsea.png 301 [0.01, **********:47002] [django.channels.server:174]
2025-08-19 13:26:44: INFO HTTP GET /static/vendors/images/logo_3500.gif 301 [0.02, **********:47026] [django.channels.server:174]
2025-08-19 13:26:45: INFO HTTP GET /static/jquery.tagsinput-revisited-2.0.min.css 200 [0.83, **********:52068] [django.channels.server:168]
2025-08-19 13:26:45: INFO HTTP GET /static/jquery-ui-1.12.1.min.css 200 [1.00, **********:47040] [django.channels.server:168]
2025-08-19 13:26:45: INFO HTTP GET /static/vendors/images/landing/slider_single/chelsea.png 200 [1.12, **********:47056] [django.channels.server:168]
2025-08-19 13:26:45: INFO HTTP GET /static/vendors/images/logo_3500.gif 200 [1.31, **********:47066] [django.channels.server:168]
2025-08-19 13:26:45: INFO HTTP GET /static/vendors/images/landing/slider_single/heather.png 301 [0.42, **********:47026] [django.channels.server:174]
2025-08-19 13:26:45: INFO HTTP GET /static/vendors/images/landing/slider_single/lindsay.png 301 [0.42, **********:47002] [django.channels.server:174]
2025-08-19 13:26:45: INFO HTTP GET /static/vendors/images/landing/slider_single/alexia.png 301 [0.43, **********:47018] [django.channels.server:174]
2025-08-19 13:26:45: INFO HTTP GET /static/jquery-3.2.1.min.js 301 [0.41, **********:47078] [django.channels.server:174]
2025-08-19 13:26:45: INFO HTTP GET /static/jquery-ui-1.12.1.min.js 301 [0.32, **********:47090] [django.channels.server:174]
2025-08-19 13:26:46: INFO HTTP GET /static/vendors/images/landing/slider_single/lindsay.png 200 [0.37, **********:47040] [django.channels.server:168]
2025-08-19 13:26:46: INFO HTTP GET /static/vendors/images/landing/slider_single/heather.png 200 [0.41, **********:47056] [django.channels.server:168]
2025-08-19 13:26:46: INFO HTTP GET /static/vendors/images/landing/slider_single/alexia.png 200 [0.46, **********:52068] [django.channels.server:168]
2025-08-19 13:26:46: INFO HTTP GET /static/jquery-3.2.1.min.js 200 [0.64, **********:47106] [django.channels.server:168]
2025-08-19 13:26:46: INFO HTTP GET /static/jquery-ui-1.12.1.min.js 200 [0.70, **********:47118] [django.channels.server:168]
2025-08-19 13:26:46: INFO HTTP GET /static/jquery.tagsinput-revisited-2.0.min.js 301 [0.59, **********:47090] [django.channels.server:174]
2025-08-19 13:26:46: INFO HTTP GET /static/vendors/images/landing/slider_solutions/wordpress-website.jpg 301 [0.38, **********:47078] [django.channels.server:174]
2025-08-19 13:26:46: INFO HTTP GET /static/vendors/images/landing/slider_solutions/postcards.jpg 301 [0.36, **********:47018] [django.channels.server:174]
2025-08-19 13:26:46: INFO HTTP GET /static/vendors/images/landing/slider_solutions/logo-design.jpg 301 [0.33, **********:47002] [django.channels.server:174]
2025-08-19 13:26:46: INFO HTTP GET /static/vendors/images/landing/slider_solutions/bov.jpg 301 [0.12, **********:47026] [django.channels.server:174]
2025-08-19 13:26:47: INFO HTTP GET /static/vendors/images/landing/slider_solutions/wordpress-website.jpg 200 [0.88, **********:47106] [django.channels.server:168]
2025-08-19 13:26:47: INFO HTTP GET /static/vendors/images/landing/slider_solutions/postcards.jpg 200 [1.10, **********:52068] [django.channels.server:168]
2025-08-19 13:26:48: INFO HTTP GET /static/jquery.tagsinput-revisited-2.0.min.js 200 [1.41, **********:47056] [django.channels.server:168]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/slider_solutions/branding.jpg 301 [1.36, **********:47026] [django.channels.server:174]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/slider_solutions/logo-design.jpg 200 [1.41, **********:47118] [django.channels.server:168]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/slider_solutions/bov.jpg 200 [1.45, **********:47040] [django.channels.server:168]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/slider_solutions/branding-logo-design.jpg 301 [0.63, **********:47002] [django.channels.server:174]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/slider_solutions/om.jpg 301 [0.44, **********:47018] [django.channels.server:174]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/slider_solutions/company-profile.jpg 301 [0.12, **********:47026] [django.channels.server:174]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/slider_solutions/retail-amenities-maps.jpg 301 [0.03, **********:47090] [django.channels.server:174]
2025-08-19 13:26:48: INFO HTTP GET /static/vendors/images/landing/fcrelogo.svg 301 [0.03, **********:47002] [django.channels.server:174]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/landing/slider_solutions/branding.jpg 200 [0.23, **********:47040] [django.channels.server:168]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/landing/slider_solutions/branding-logo-design.jpg 200 [0.28, **********:47118] [django.channels.server:168]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/landing/slider_solutions/om.jpg 200 [0.33, **********:47056] [django.channels.server:168]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/landing/slider_solutions/company-profile.jpg 200 [0.58, **********:52068] [django.channels.server:168]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/landing/slider_solutions/retail-amenities-maps.jpg 200 [0.62, **********:47106] [django.channels.server:168]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/landing/fcrelogo.svg 200 [0.64, **********:47066] [django.channels.server:168]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/favicons/favicon.ico 301 [0.00, **********:47002] [django.channels.server:174]
2025-08-19 13:26:49: INFO HTTP GET /static/vendors/images/favicons/favicon.ico 200 [0.27, **********:47066] [django.channels.server:168]
2025-08-19 13:26:50: INFO HTTP GET /static/jquery-3.2.1.min.js 200 [0.38, **********:47122] [django.channels.server:168]
2025-08-19 13:26:52: INFO HTTP GET /static/jquery-ui-1.12.1.min.js 200 [0.19, **********:47122] [django.channels.server:168]
2025-08-19 13:26:52: INFO HTTP GET /static/jquery.tagsinput-revisited-2.0.min.js 200 [0.33, **********:53300] [django.channels.server:168]
2025-08-19 13:26:59: INFO HTTP GET / 200 [0.26, **********:47066] [django.channels.server:168]
2025-08-19 13:27:01: INFO HTTP GET /favicon.ico 302 [0.12, **********:47066] [django.channels.server:174]
2025-08-19 13:27:08: INFO HTTP GET /static/jquery-3.2.1.min.js 301 [0.00, **********:36792] [django.channels.server:174]
2025-08-19 13:30:06: INFO HTTP GET / 200 [0.20, **********:38978] [django.channels.server:168]
2025-08-19 13:30:06: INFO HTTP GET /favicon.ico 302 [0.17, **********:38978] [django.channels.server:174]
